import os
from typing import List

class Settings:
    # S3 Configuration
    S3_BUCKET_NAME: str = "noaa-wcsd-pds"
    S3_PREFIX: str = "data/raw/<PERSON>_Lasker/RL2107/EK80"
    
    # CORS Configuration
    ALLOWED_ORIGINS: List[str] = [
        "https://noaadataset.netlify.app",
        "http://localhost:3000",
        "http://localhost:5173"
    ]
    
    # File Storage
    UPLOAD_DIR: str = "uploads"
    TEMP_DIR: str = "/tmp"
    LOCAL_RAW_DIR: str = "E:/4/MVP/Nouveau dossier/traitement/SH2106_EK80"
    
    # Database (if needed later)
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./app.db")
    
    # ML Models
    MODELS_DIR: str = "models"
    
    # Logging
    LOG_LEVEL: str = "INFO"

settings = Settings()

