from fastapi import API<PERSON>outer, HTTPException, Query
from fastapi.responses import StreamingResponse
import io
import logging

from ..models.schemas import EchogramRequest, SpectrogramRequest
from ..services.visualization_service import visualization_service
from .data_management import get_loaded_file_data

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/visualize", tags=["Visualization"])

@router.post("/echogram")
async def generate_echogram_api(request: EchogramRequest):
    """
    Génère un échogramme pour un fichier RAW chargé.
    """
    try:
        file_data = get_loaded_file_data(request.file_id)
        ek80_data = file_data["ek80_data"]
        
        # For simplicity, we'll use the first channel found or a specific one if available
        channel_id = next(
            (ch for ch in ek80_data.channel_ids if '38' in ch or 'ES38' in ch),
            ek80_data.channel_ids[0]
        )

        echogram_bytes = visualization_service.generate_echogram(
            ek80_data,
            channel_id,
            request.saturation_threshold,
            request.percentile_low,
            request.percentile_high,
            request.min_range_db,
            request.max_depth
        )
        
        headers = {
            'Content-Disposition': f'inline; filename="echogram_{request.file_id}.png"',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': '*',
            'Access-Control-Expose-Headers': 'Content-Disposition'
        }
        
        return StreamingResponse(echogram_bytes, media_type="image/png", headers=headers)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error generating echogram: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/spectrogram")
async def generate_spectrogram_api(request: SpectrogramRequest):
    """
    Génère un spectrogramme pour un fichier RAW chargé.
    """
    try:
        file_data = get_loaded_file_data(request.file_id)
        ek80_data = file_data["ek80_data"]

        # For simplicity, we'll use the first channel found or a specific one if available
        channel_id = next(
            (ch for ch in ek80_data.channel_ids if str(request.frequency) in ch or f'ES{int(request.frequency/1000)}' in ch),
            ek80_data.channel_ids[0]
        )

        spectrogram_bytes = visualization_service.generate_spectrogram(
            ek80_data,
            channel_id,
            request.ping_range,
            request.frequency,
            request.window_size,
            request.overlap
        )

        headers = {
            'Content-Disposition': f'inline; filename="spectrogram_{request.file_id}.png"',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': '*',
            'Access-Control-Expose-Headers': 'Content-Disposition'
        }

        return StreamingResponse(spectrogram_bytes, media_type="image/png", headers=headers)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error generating spectrogram: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


