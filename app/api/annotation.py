from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import logging

from ..models.schemas import (
    Annotation,
    CreateAnnotationRequest,
    StatusResponse
)
from ..services.annotation_service import annotation_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/annotations", tags=["Annotations"])

@router.post("/", response_model=Annotation)
async def create_annotation(annotation_data: CreateAnnotationRequest):
    """
    Crée une nouvelle annotation.
    """
    try:
        annotation = annotation_service.create_annotation(annotation_data)
        return annotation
    except Exception as e:
        logger.error(f"Error creating annotation: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/{annotation_id}", response_model=Annotation)
async def get_annotation(annotation_id: str):
    """
    Récupère une annotation par son ID.
    """
    try:
        annotation = annotation_service.get_annotation(annotation_id)
        return annotation
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting annotation: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/file/{file_id}", response_model=List[Annotation])
async def get_annotations_by_file(file_id: str):
    """
    Récupère toutes les annotations pour un fichier donné.
    """
    try:
        annotations = annotation_service.get_annotations_by_file_id(file_id)
        return annotations
    except Exception as e:
        logger.error(f"Error getting annotations for file {file_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.put("/{annotation_id}", response_model=Annotation)
async def update_annotation(annotation_id: str, update_data: Dict[str, Any]):
    """
    Met à jour une annotation existante.
    """
    try:
        annotation = annotation_service.update_annotation(annotation_id, update_data)
        return annotation
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating annotation: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/{annotation_id}", response_model=StatusResponse)
async def delete_annotation(annotation_id: str):
    """
    Supprime une annotation.
    """
    try:
        annotation_service.delete_annotation(annotation_id)
        return StatusResponse(
            status="success",
            message=f"Annotation {annotation_id} deleted successfully"
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error deleting annotation: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

