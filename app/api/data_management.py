from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
import uuid
import logging
import os

from ..models.schemas import (
    LoadRawFileRequest, 
    LoadRawFileResponse, 
    RawFileInfo,
    StatusResponse
)
from ..services.raw_data_service import raw_data_service
from ..core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/data", tags=["Data Management"])

# Stockage en mémoire pour les informations sur les fichiers
loaded_files: Dict[str, Dict[str, Any]] = {}

# --- Lazy Loading Helper ---
def ensure_file_is_loaded(file_id: str) -> Dict[str, Any]:
    """
    Vérifie si les données d'un fichier sont chargées en mémoire.
    Si non, les charge depuis le fichier local. Retourne les données du fichier.
    """
    if file_id not in loaded_files:
        raise HTTPException(status_code=404, detail="File ID not found")

    file_data = loaded_files[file_id]

    # Si les données ne sont pas encore chargées, on le fait maintenant
    if file_data.get("ek80_data") is None:
        logger.info(f"Lazy loading data for file: {file_data['filename']}")
        try:
            local_path = file_data["local_path"]
            if not os.path.exists(local_path):
                raise FileNotFoundError(f"File not found at path: {local_path}")
            
            # Traitement et extraction des métadonnées
            ek80_data = raw_data_service.process_raw_file(local_path)
            metadata = raw_data_service.get_file_metadata(local_path)
            
            # Mise à jour du dictionnaire en mémoire
            loaded_files[file_id]["ek80_data"] = ek80_data
            loaded_files[file_id]["metadata"] = metadata
            logger.info(f"Successfully loaded data for: {file_data['filename']}")

        except Exception as e:
            logger.error(f"Failed to lazy load file {file_data['filename']}: {e}", exc_info=True)
            # Supprimer le fichier de la liste s'il ne peut être chargé
            loaded_files.pop(file_id, None)
            raise HTTPException(status_code=500, detail=f"Failed to process file: {e}")

    return loaded_files[file_id]


@router.post("/load-raw-file", response_model=LoadRawFileResponse)
async def load_raw_file(request: LoadRawFileRequest):
    """
    Charge un fichier .raw depuis S3. Si le fichier est déjà connu localement,
    retourne son ID. Sinon, le télécharge.
    """
    # Vérifier si un fichier avec le même nom est déjà connu
    for file_id, data in loaded_files.items():
        if data["filename"] == request.filename:
            logger.info(f"File '{request.filename}' is already known with ID {file_id}. Returning existing ID.")
            # On s'assure qu'il est chargé avant de retourner
            ensure_file_is_loaded(file_id)
            return LoadRawFileResponse(
                file_id=file_id,
                filename=request.filename,
                status="success",
                message="File already exists locally and is ready.",
                metadata=loaded_files[file_id]["metadata"]
            )

    # Si le fichier n'est pas connu et que la source est S3
    if request.source == "s3":
        try:
            logger.info(f"File '{request.filename}' not found locally, downloading from S3.")
            local_path = raw_data_service.download_raw_file_from_s3(request.filename)
            
            file_id = str(uuid.uuid4())
            # On ne charge que les métadonnées pour la réponse initiale
            metadata = raw_data_service.get_file_metadata(local_path)

            loaded_files[file_id] = {
                "file_id": file_id,
                "filename": request.filename,
                "local_path": local_path,
                "ek80_data": None, # Chargement paresseux
                "metadata": metadata,
                "source": "s3"
            }

            return LoadRawFileResponse(
                file_id=file_id,
                filename=request.filename,
                status="success",
                message=f"File '{request.filename}' downloaded successfully from S3.",
                metadata=metadata
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    raise HTTPException(status_code=404, detail=f"File '{request.filename}' not found locally and source is not S3.")


@router.get("/files", response_model=List[RawFileInfo])
async def list_loaded_files():
    """
    Liste tous les fichiers connus (locaux et téléchargés).
    """
    files_info = []
    for file_id, file_data in loaded_files.items():
        # Pour les fichiers non encore traités, on essaie de lire les métadonnées à la volée
        if file_data.get("metadata") is None:
            try:
                ensure_file_is_loaded(file_id)
                file_data = loaded_files[file_id] # Re-fetch after loading
            except HTTPException:
                # Si le chargement échoue, on l'ignore dans la liste
                continue

        files_info.append(RawFileInfo(
            file_id=file_data["file_id"],
            filename=file_data["filename"],
            size=os.path.getsize(file_data["local_path"]) if os.path.exists(file_data["local_path"]) else 0,
            metadata=file_data["metadata"]
        ))
    return files_info


@router.get("/files/{file_id}", response_model=RawFileInfo)
async def get_file_info(file_id: str):
    """
    Récupère les informations d'un fichier spécifique, en le chargeant si nécessaire.
    """
    file_data = ensure_file_is_loaded(file_id)
    return RawFileInfo(
        file_id=file_data["file_id"],
        filename=file_data["filename"],
        size=os.path.getsize(file_data["local_path"]) if os.path.exists(file_data["local_path"]) else 0,
        metadata=file_data["metadata"]
    )


@router.delete("/files/{file_id}", response_model=StatusResponse)
async def delete_file(file_id: str):
    """
    Décharge un fichier de la mémoire (ne supprime pas le fichier physique).
    """
    if file_id not in loaded_files:
        raise HTTPException(status_code=404, detail="File not found")
    
    file_data = loaded_files.pop(file_id)
    logger.info(f"File {file_data['filename']} unloaded from memory.")
    
    return StatusResponse(
        status="success",
        message=f"File {file_data['filename']} unloaded from memory."
    )


@router.get("/files/{file_id}/channels")
async def get_file_channels(file_id: str):
    """
    Récupère la liste des canaux disponibles pour un fichier.
    """
    file_data = ensure_file_is_loaded(file_id)
    ek80_data = file_data["ek80_data"]
    
    try:
        channel_data = ek80_data.get_channel_data()
        channels_info = {}
        for channel_id, channel_obj in channel_data.items():
            channels_info[channel_id] = {
                "frequency": getattr(channel_obj, 'frequency', None),
                "ping_count": len(ek80_data.raw_data.get(channel_id, []))
            }
        
        if not channels_info:
            return {"message": "No channels found for this file."}
            
        return channels_info
    except Exception as e:
        logger.error(f"Error getting channels for file {file_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred while getting channel data: {e}")


# Helper function to get loaded file data (used by other modules)
def get_loaded_file_data(file_id: str) -> Dict[str, Any]:
    """
    Fonction utilitaire pour récupérer les données d'un fichier chargé (avec chargement paresseux).
    """
    return ensure_file_is_loaded(file_id)
