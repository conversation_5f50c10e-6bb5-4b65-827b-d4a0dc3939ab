from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import logging

from ..models.schemas import StatusR<PERSON>ponse, Prediction
from ..services.analysis_service import analysis_service
from ..services.ml_model_service import ml_model_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/analysis", tags=["Analysis"])

@router.get("/model-performance/{model_id}")
async def get_model_performance_analysis(model_id: str):
    """
    Récupère l'analyse de performance d'un modèle ML.
    """
    try:
        analysis_result = analysis_service.analyze_model_performance(model_id)
        return analysis_result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting model performance analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/predictions", response_model=Dict[str, Any])
async def analyze_predictions_api(predictions: List[Prediction]):
    """
    Analyse un ensemble de prédictions.
    """
    try:
        analysis_result = analysis_service.analyze_predictions(predictions)
        return analysis_result
    except Exception as e:
        logger.error(f"Error analyzing predictions: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/compare-models", response_model=Dict[str, Any])
async def compare_ml_models(model_ids: List[str]):
    """
    Compare les performances de plusieurs modèles ML.
    """
    try:
        comparison_result = analysis_service.compare_models(model_ids)
        return comparison_result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error comparing models: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


