from fastapi import APIRouter, HTTPException
import logging

from ..models.schemas import FeatureExtractionRequest
from ..services.feature_extraction_service import feature_extraction_service
from .data_management import get_loaded_file_data

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/features", tags=["Feature Extraction"])

@router.post("/extract")
async def extract_features_api(request: FeatureExtractionRequest):
    """
    Extrait des caractéristiques acoustiques à partir des données RAW.
    """
    try:
        file_data = get_loaded_file_data(request.file_id)
        ek80_data = file_data["ek80_data"]

        # For simplicity, we'll use the first channel found or a specific one if available
        channel_id = next(
            (ch for ch in ek80_data.channel_ids if '38' in ch or 'ES38' in ch),
            ek80_data.channel_ids[0]
        )

        extracted_features = feature_extraction_service.extract_features(
            ek80_data,
            channel_id,
            request.ping_ranges,
            request.frequencies,
            request.window_sizes,
            request.depth_range,
            request.feature_types
        )

        return {
            "status": "success",
            "file_id": request.file_id,
            "channel_id": channel_id,
            "features": extracted_features
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error extracting features: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

