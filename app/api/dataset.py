from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import logging

from ..models.schemas import (
    Dataset,
    CreateDatasetRequest,
    StatusResponse
)
from ..services.dataset_service import dataset_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/datasets", tags=["Datasets"])

@router.post("/", response_model=Dataset)
async def create_dataset(dataset_data: CreateDatasetRequest):
    """
    Crée un nouveau dataset.
    """
    try:
        dataset = dataset_service.create_dataset(dataset_data)
        return dataset
    except Exception as e:
        logger.error(f"Error creating dataset: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/", response_model=List[Dataset])
async def list_datasets():
    """
    Liste tous les datasets.
    """
    try:
        datasets = dataset_service.list_datasets()
        return datasets
    except Exception as e:
        logger.error(f"Error listing datasets: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/{dataset_id}", response_model=Dataset)
async def get_dataset(dataset_id: str):
    """
    Récupère un dataset par son ID.
    """
    try:
        dataset = dataset_service.get_dataset(dataset_id)
        return dataset
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting dataset: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.put("/{dataset_id}", response_model=Dataset)
async def update_dataset(dataset_id: str, update_data: Dict[str, Any]):
    """
    Met à jour un dataset existant.
    """
    try:
        dataset = dataset_service.update_dataset(dataset_id, update_data)
        return dataset
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating dataset: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/{dataset_id}", response_model=StatusResponse)
async def delete_dataset(dataset_id: str):
    """
    Supprime un dataset.
    """
    try:
        dataset_service.delete_dataset(dataset_id)
        return StatusResponse(
            status="success",
            message=f"Dataset {dataset_id} deleted successfully"
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error deleting dataset: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/{dataset_id}/files/{file_id}", response_model=Dataset)
async def add_file_to_dataset(dataset_id: str, file_id: str):
    """
    Ajoute un fichier à un dataset.
    """
    try:
        dataset = dataset_service.add_file_to_dataset(dataset_id, file_id)
        return dataset
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding file to dataset: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/{dataset_id}/annotations/{annotation_id}", response_model=Dataset)
async def add_annotation_to_dataset(dataset_id: str, annotation_id: str):
    """
    Ajoute une annotation à un dataset.
    """
    try:
        dataset = dataset_service.add_annotation_to_dataset(dataset_id, annotation_id)
        return dataset
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding annotation to dataset: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

