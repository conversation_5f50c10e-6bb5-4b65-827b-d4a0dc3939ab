from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import logging

from ..models.schemas import (
    MLModel,
    TrainModelRequest,
    PredictionRequest,
    Prediction,
    StatusResponse
)
from ..services.ml_model_service import ml_model_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/models", tags=["ML Models"])

@router.post("/train", response_model=MLModel)
async def train_model(request: TrainModelRequest):
    """
    Lance l'entraînement d'un nouveau modèle ML.
    """
    try:
        model = ml_model_service.train_model(request)
        return model
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error training model: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/", response_model=List[MLModel])
async def list_models():
    """
    Liste tous les modèles ML entraînés.
    """
    try:
        models = ml_model_service.list_models()
        return models
    except Exception as e:
        logger.error(f"Error listing models: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/{model_id}", response_model=MLModel)
async def get_model(model_id: str):
    """
    Récupère un modèle ML par son ID.
    """
    try:
        model = ml_model_service.get_model(model_id)
        return model
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting model: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/{model_id}", response_model=StatusResponse)
async def delete_model(model_id: str):
    """
    Supprime un modèle ML.
    """
    try:
        ml_model_service.delete_model(model_id)
        return StatusResponse(
            status="success",
            message=f"ML Model {model_id} deleted successfully"
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error deleting model: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/predict", response_model=List[Prediction])
async def predict_with_model(request: PredictionRequest):
    """
    Effectue des prédictions en utilisant un modèle entraîné.
    """
    try:
        predictions = ml_model_service.predict(
            model_id=request.model_id,
            file_id=request.file_id,
            ping_range=request.ping_range,
            confidence_threshold=request.confidence_threshold
        )
        return predictions
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error during prediction: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


