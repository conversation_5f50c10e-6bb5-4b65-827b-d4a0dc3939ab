from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

# Schémas pour les données RAW
class RawFileInfo(BaseModel):
    filename: str
    size: int
    last_modified: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

class LoadRawFileRequest(BaseModel):
    filename: str
    source: str = "s3"  # s3, local, url
    source_path: Optional[str] = None

class LoadRawFileResponse(BaseModel):
    file_id: str
    filename: str
    status: str
    message: str
    metadata: Optional[Dict[str, Any]] = None

# Schémas pour la visualisation
class EchogramRequest(BaseModel):
    file_id: str
    saturation_threshold: float = Field(-10, description="Saturation threshold in dB")
    percentile_low: float = Field(1, description="Lower percentile for color range")
    percentile_high: float = Field(99, description="Upper percentile for color range")
    min_range_db: float = Field(20, description="Minimum color range in dB")
    max_depth: float = Field(100, description="Maximum depth to display")

class SpectrogramRequest(BaseModel):
    file_id: str
    ping_range: List[int] = Field(..., description="[start_ping, end_ping]")
    frequency: int = Field(38000, description="Frequency in Hz")
    window_size: float = Field(0.1, description="Window size in seconds")
    overlap: float = Field(0.5, description="Overlap ratio (0-1)")

# Schémas pour l'extraction de caractéristiques
class FeatureExtractionRequest(BaseModel):
    file_id: str
    ping_ranges: List[List[int]] = Field(..., description="List of [start, end] ping indices")
    frequencies: List[int] = Field(..., description="List of frequencies in Hz")
    window_sizes: List[float] = Field(..., description="List of window sizes in seconds")
    depth_range: List[float] = Field(..., description="[min_depth, max_depth] in meters")
    feature_types: List[str] = Field(["mfcc", "spectral_centroid", "bandwidth"], description="Types of features to extract")

# Schémas pour les annotations
class Annotation(BaseModel):
    id: Optional[str] = None
    file_id: str
    x: float
    y: float
    width: float
    height: float
    label: str
    confidence: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None

class CreateAnnotationRequest(BaseModel):
    file_id: str
    x: float
    y: float
    width: float
    height: float
    label: str
    confidence: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

# Schémas pour les datasets
class Dataset(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    file_ids: List[str]
    annotation_ids: List[str]
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class CreateDatasetRequest(BaseModel):
    name: str
    description: Optional[str] = None
    file_ids: List[str]
    annotation_ids: List[str]

# Schémas pour les modèles ML
class MLModel(BaseModel):
    id: Optional[str] = None
    name: str
    model_type: str  # "cnn", "lstm", "random_forest", etc.
    architecture: Optional[Dict[str, Any]] = None
    hyperparameters: Optional[Dict[str, Any]] = None
    training_dataset_id: Optional[str] = None
    performance_metrics: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class TrainModelRequest(BaseModel):
    name: str
    model_type: str
    dataset_id: str
    hyperparameters: Optional[Dict[str, Any]] = None
    validation_split: float = Field(0.2, description="Validation split ratio")

class PredictionRequest(BaseModel):
    model_id: str
    file_id: str
    ping_range: Optional[List[int]] = None
    confidence_threshold: float = Field(0.5, description="Minimum confidence for predictions")

class Prediction(BaseModel):
    id: Optional[str] = None
    model_id: str
    file_id: str
    x: float
    y: float
    width: float
    height: float
    predicted_class: str
    confidence: float
    created_at: Optional[datetime] = None

# Schémas pour les projets
class Project(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    file_ids: List[str] = []
    dataset_ids: List[str] = []
    model_ids: List[str] = []
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class CreateProjectRequest(BaseModel):
    name: str
    description: Optional[str] = None

# Schémas de réponse génériques
class StatusResponse(BaseModel):
    status: str
    message: str
    data: Optional[Any] = None

class ErrorResponse(BaseModel):
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None

