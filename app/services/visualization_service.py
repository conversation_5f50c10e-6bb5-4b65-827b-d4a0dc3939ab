import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import io
import gc
from scipy.signal import spectrogram

# Utility functions from main.py
def apply_ek80_power_correction(complex_data, raw_data, channel_id):
    try:
        frequency = getattr(raw_data, 'frequency', 38000)
        gain = getattr(raw_data, 'gain', 0)
        sa_correction = getattr(raw_data, 'sa_correction', 0)
        absorption = getattr(raw_data, 'absorption', 0) # dB/km
        
        pulse_length = getattr(raw_data, 'pulse_length', 0.001)
        sound_velocity = raw_data.sound_velocity[0]
        sample_interval = raw_data.sample_interval[0]
        
        n_samples = complex_data.shape[1]
        ranges = np.arange(n_samples) * sample_interval * sound_velocity / 2
        
        absorption_correction = absorption * ranges / 1000
        
        magnitude_squared = np.abs(complex_data) ** 2
        
        calibration_factor = 10 ** ((gain + sa_correction) / 10)
        
        absorption_correction_expanded = np.tile(absorption_correction, (complex_data.shape[0], 1))
        
        corrected_power = magnitude_squared * calibration_factor * (10 ** (absorption_correction_expanded / 10))
        
        power_db = 10 * np.log10(corrected_power + 1e-12)
        
        return power_db
        
    except Exception as e:
        print(f"Warning: EK80 power correction failed, using basic power calculation: {e}")
        magnitude_squared = np.abs(complex_data) ** 2
        return 10 * np.log10(magnitude_squared + 1e-12)

def detect_and_handle_saturation(power_db, saturation_threshold_db=-10):
    saturation_mask = power_db > saturation_threshold_db
    saturated_pixels = np.sum(saturation_mask)
    total_pixels = power_db.size
    saturation_percentage = (saturated_pixels / total_pixels) * 100
    
    if saturation_percentage > 5:
        from scipy.ndimage import median_filter
        processed_power = power_db.copy()
        smoothed = median_filter(processed_power, size=3)
        processed_power[saturation_mask] = smoothed[saturation_mask]
        return processed_power, saturation_mask
    else:
        return power_db, saturation_mask

def calculate_optimal_color_range(power_db, percentile_low=1, percentile_high=99, min_range_db=20):
    valid_data = power_db[np.isfinite(power_db)]
    
    if len(valid_data) == 0:
        return -100, 0
    
    p_low = np.percentile(valid_data, percentile_low)
    p_high = np.percentile(valid_data, percentile_high)
    
    data_range = p_high - p_low
    if data_range < min_range_db:
        mid_point = (p_low + p_high) / 2
        p_low = mid_point - min_range_db / 2
        p_high = mid_point + min_range_db / 2
    
    return p_low, p_high

class VisualizationService:
    def generate_echogram(self, ek80_data, channel_id: str, saturation_threshold: float, percentile_low: float, percentile_high: float, min_range_db: float, max_depth: float):
        raw_data = ek80_data.raw_data[channel_id][0]
        
        complex_data = raw_data.get_complex().data
        ping_time = raw_data.ping_time
        sample_interval = raw_data.sample_interval[0]
        sound_velocity = raw_data.sound_velocity[0]
        n_samples = raw_data.n_samples

        depth_per_sample = np.arange(n_samples) * sample_interval * sound_velocity / 2
        depth_mask = depth_per_sample <= max_depth
        depth_axis = depth_per_sample[depth_mask]
        n_depth = len(depth_axis)

        power_db = apply_ek80_power_correction(complex_data, raw_data, channel_id)
        power_db, saturation_mask = detect_and_handle_saturation(power_db, saturation_threshold_db=saturation_threshold)

        n_pings = power_db.shape[0]
        normalized_power = np.full((n_pings, n_depth), np.nan)
        for i in range(n_pings):
            this_ping = power_db[i, :]
            valid = min(len(this_ping), n_depth)
            normalized_power[i, :valid] = this_ping[:valid]

        masked_power = np.ma.masked_invalid(normalized_power)
        
        vmin, vmax = calculate_optimal_color_range(masked_power, percentile_low=percentile_low, percentile_high=percentile_high, min_range_db=min_range_db)

        plt.style.use('dark_background')
        fig, ax = plt.subplots(figsize=(12, 6))

        if n_pings == 1:
            ax.imshow(
                masked_power.T,
                aspect='auto',
                origin='upper',
                extent=[0, 1, max_depth, 0],
                cmap='jet',
                vmin=vmin,
                vmax=vmax
            )
            ax.set_xlabel('Ping (single)')
        else:
            ping_time_mpl = mdates.date2num(ping_time)
            ax.imshow(
                masked_power.T,
                aspect='auto',
                origin='upper',
                extent=[ping_time_mpl[0], ping_time_mpl[-1], max_depth, 0],
                cmap='jet',
                vmin=vmin,
                vmax=vmax
            )
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            ax.xaxis.set_major_locator(mdates.AutoDateLocator())
            ax.set_xlabel('Time (pings)')

        plt.colorbar(ax.images[0], ax=ax, label='Power (dB)')
        ax.set_title(f'Echogram - EK80 Corrected Power - {channel_id} (Range: {vmin:.1f} to {vmax:.1f} dB, Saturation Threshold: {saturation_threshold} dB)')
        ax.set_ylabel('Depth (m)')
        ax.set_ylim(max_depth, 0)
        plt.tight_layout()

        echogram_bytes = io.BytesIO()
        plt.savefig(echogram_bytes, dpi=150, bbox_inches='tight', format='png')
        plt.clf()
        plt.close('all')
        del fig, ax, masked_power, normalized_power, power_db, complex_data
        gc.collect()
        echogram_bytes.seek(0)
        
        return echogram_bytes

    def generate_spectrogram(self, ek80_data, channel_id: str, ping_range: list, frequency: int, window_size: float, overlap: float):
        raw_data = ek80_data.raw_data[channel_id][0]
        complex_data = raw_data.get_complex().data
        sample_interval = raw_data.sample_interval[0]
        sound_velocity = raw_data.sound_velocity[0]

        start_ping, end_ping = ping_range
        selected_data = complex_data[start_ping:end_ping+1, :]

        # Calculate time axis for spectrogram
        n_samples = selected_data.shape[1]
        time_axis = np.arange(n_samples) * sample_interval

        # Spectrogram parameters
        fs = 1.0 / sample_interval  # Sampling frequency
        nperseg = int(window_size * fs) # Samples per segment
        noverlap = int(nperseg * overlap) # Overlap samples

        # Compute spectrogram for each ping and average
        # This is a simplified approach, a proper spectrogram would be more complex
        # and might involve more advanced signal processing.
        all_spectrograms = []
        for i in range(selected_data.shape[0]):
            f, t, Sxx = spectrogram(selected_data[i, :], fs, nperseg=nperseg, noverlap=noverlap)
            all_spectrograms.append(10 * np.log10(Sxx + 1e-12))
        
        if not all_spectrograms:
            raise ValueError("No data to generate spectrogram.")

        avg_spectrogram = np.mean(all_spectrograms, axis=0)

        plt.style.use('dark_background')
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Transpose Sxx for correct plotting (time on x-axis, frequency on y-axis)
        # extent=[xmin, xmax, ymin, ymax]
        im = ax.imshow(avg_spectrogram.T, aspect='auto', origin='lower', cmap='jet', extent=[t.min(), t.max(), f.min(), f.max()])      
        ax.set_title(f'Spectrogram - {channel_id} ({frequency/1000:.1f} kHz) - Pings {start_ping}-{end_ping}')
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Frequency (Hz)')
        plt.colorbar(im, ax=ax, label='Power (dB)')
        plt.tight_layout()

        spectrogram_bytes = io.BytesIO()
        plt.savefig(spectrogram_bytes, dpi=150, bbox_inches='tight', format='png')
        plt.clf()
        plt.close('all')
        del fig, ax, Sxx, f, t, selected_data, all_spectrograms, avg_spectrogram
        gc.collect()
        spectrogram_bytes.seek(0)

        return spectrogram_bytes

visualization_service = VisualizationService()


