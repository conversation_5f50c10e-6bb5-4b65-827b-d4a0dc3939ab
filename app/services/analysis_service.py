from typing import Dict, Any, List
import numpy as np
import logging

from ..models.schemas import Prediction, MLModel
from .ml_model_service import ml_model_service

logger = logging.getLogger(__name__)

class AnalysisService:
    def analyze_model_performance(self, model_id: str) -> Dict[str, Any]:
        """
        Analyse les performances d'un modèle ML.
        """
        try:
            model = ml_model_service.get_model(model_id)
            
            # In a real application, this would involve:
            # 1. Loading validation/test data
            # 2. Running predictions on the validation/test set
            # 3. Computing various metrics (accuracy, precision, recall, F1-score, confusion matrix, etc.)
            # 4. Generating performance visualizations (ROC curves, PR curves, etc.)
            
            # For now, we'll return the stored performance metrics and add some additional analysis
            performance_analysis = {
                "model_id": model_id,
                "model_name": model.name,
                "model_type": model.model_type,
                "training_dataset_id": model.training_dataset_id,
                "stored_metrics": model.performance_metrics or {},
                "analysis": {
                    "status": "completed" if model.performance_metrics else "pending",
                    "recommendations": self._generate_recommendations(model)
                }
            }
            
            return performance_analysis
        except ValueError as e:
            raise e
        except Exception as e:
            logger.error(f"Error analyzing model performance: {e}")
            raise RuntimeError(f"Performance analysis failed: {str(e)}")

    def _generate_recommendations(self, model: MLModel) -> List[str]:
        """
        Génère des recommandations basées sur les performances du modèle.
        """
        recommendations = []
        
        if model.performance_metrics:
            accuracy = model.performance_metrics.get("accuracy", 0)
            loss = model.performance_metrics.get("loss", 1)
            
            if accuracy < 0.8:
                recommendations.append("Consider increasing training data or adjusting hyperparameters")
            if loss > 0.2:
                recommendations.append("Model may be overfitting - consider regularization techniques")
            if accuracy > 0.95:
                recommendations.append("Excellent performance - model is ready for production")
        else:
            recommendations.append("No performance metrics available - run evaluation first")
        
        return recommendations

    def analyze_predictions(self, predictions: List[Prediction]) -> Dict[str, Any]:
        """
        Analyse un ensemble de prédictions.
        """
        if not predictions:
            return {
                "total_predictions": 0,
                "confidence_stats": {},
                "class_distribution": {},
                "spatial_distribution": {}
            }
        
        confidences = [pred.confidence for pred in predictions]
        classes = [pred.predicted_class for pred in predictions]
        
        # Confidence statistics
        confidence_stats = {
            "mean": np.mean(confidences),
            "std": np.std(confidences),
            "min": np.min(confidences),
            "max": np.max(confidences),
            "median": np.median(confidences)
        }
        
        # Class distribution
        unique_classes, counts = np.unique(classes, return_counts=True)
        class_distribution = dict(zip(unique_classes, counts.tolist()))
        
        # Spatial distribution (simplified)
        x_coords = [pred.x for pred in predictions]
        y_coords = [pred.y for pred in predictions]
        spatial_distribution = {
            "x_range": [np.min(x_coords), np.max(x_coords)],
            "y_range": [np.min(y_coords), np.max(y_coords)],
            "center_of_mass": [np.mean(x_coords), np.mean(y_coords)]
        }
        
        analysis_result = {
            "total_predictions": len(predictions),
            "confidence_stats": confidence_stats,
            "class_distribution": class_distribution,
            "spatial_distribution": spatial_distribution,
            "high_confidence_predictions": len([p for p in predictions if p.confidence > 0.8]),
            "low_confidence_predictions": len([p for p in predictions if p.confidence < 0.5])
        }
        
        return analysis_result

    def compare_models(self, model_ids: List[str]) -> Dict[str, Any]:
        """
        Compare les performances de plusieurs modèles.
        """
        try:
            models_comparison = []
            
            for model_id in model_ids:
                model = ml_model_service.get_model(model_id)
                model_info = {
                    "model_id": model_id,
                    "name": model.name,
                    "type": model.model_type,
                    "metrics": model.performance_metrics or {},
                    "created_at": model.created_at.isoformat() if model.created_at else None
                }
                models_comparison.append(model_info)
            
            # Find best performing model (based on accuracy if available)
            best_model = None
            best_accuracy = -1
            
            for model_info in models_comparison:
                accuracy = model_info["metrics"].get("accuracy", 0)
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    best_model = model_info
            
            comparison_result = {
                "models": models_comparison,
                "best_model": best_model,
                "comparison_metrics": ["accuracy", "loss"],
                "recommendations": self._generate_comparison_recommendations(models_comparison)
            }
            
            return comparison_result
        except ValueError as e:
            raise e
        except Exception as e:
            logger.error(f"Error comparing models: {e}")
            raise RuntimeError(f"Model comparison failed: {str(e)}")

    def _generate_comparison_recommendations(self, models_comparison: List[Dict[str, Any]]) -> List[str]:
        """
        Génère des recommandations basées sur la comparaison de modèles.
        """
        recommendations = []
        
        if len(models_comparison) < 2:
            recommendations.append("Train more models to enable meaningful comparison")
            return recommendations
        
        accuracies = [m["metrics"].get("accuracy", 0) for m in models_comparison]
        
        if max(accuracies) - min(accuracies) < 0.05:
            recommendations.append("Models show similar performance - consider ensemble methods")
        else:
            recommendations.append("Significant performance differences detected - focus on best performing model")
        
        return recommendations

analysis_service = AnalysisService()

