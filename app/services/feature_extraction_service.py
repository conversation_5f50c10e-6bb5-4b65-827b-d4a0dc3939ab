import numpy as np
from scipy.signal import spectrogram
import logging

logger = logging.getLogger(__name__)

class FeatureExtractionService:
    def extract_features(
        self,
        ek80_data,
        channel_id: str,
        ping_ranges: list,
        frequencies: list,
        window_sizes: list,
        depth_range: list,
        feature_types: list
    ):
        raw_data = ek80_data.raw_data[channel_id][0]
        complex_data = raw_data.get_complex().data
        sample_interval = raw_data.sample_interval[0]
        sound_velocity = raw_data.sound_velocity[0]
        n_samples = raw_data.n_samples

        depth_per_sample = np.arange(n_samples) * sample_interval * sound_velocity / 2
        depth_mask = (depth_per_sample >= depth_range[0]) & (depth_per_sample <= depth_range[1])
        
        extracted_features = []

        for start_ping, end_ping in ping_ranges:
            selected_data = complex_data[start_ping:end_ping+1, :][:, depth_mask]
            
            if selected_data.size == 0:
                logger.warning(f"No data for ping range {start_ping}-{end_ping} and depth range {depth_range}")
                continue

            # For each frequency (though echolab2 handles channels, we might need to select specific ones)
            for freq in frequencies:
                # Assuming channel_id already selected the correct frequency data
                # If not, this would need more sophisticated channel selection based on freq
                
                for window_size in window_sizes:
                    fs = 1.0 / sample_interval
                    nperseg = int(window_size * fs)
                    noverlap = int(nperseg * 0.5) # Default overlap

                    # Compute spectrogram for the selected data
                    f, t, Sxx = spectrogram(selected_data.flatten(), fs, nperseg=nperseg, noverlap=noverlap)
                    power_spectrogram = 10 * np.log10(Sxx + 1e-12)

                    features = {
                        "ping_range": [start_ping, end_ping],
                        "frequency": freq,
                        "window_size": window_size,
                        "channel_id": channel_id
                    }

                    if "mfcc" in feature_types:
                        # MFCC calculation (simplified, requires more libraries like python_speech_features or librosa)
                        # For now, a placeholder or simplified version
                        features["mfcc"] = np.mean(power_spectrogram, axis=1).tolist() # Placeholder: mean power per frequency bin

                    if "spectral_centroid" in feature_types:
                        # Spectral Centroid (simplified)
                        if f.size > 0:
                            features["spectral_centroid"] = np.sum(f * power_spectrogram, axis=0) / np.sum(power_spectrogram, axis=0)
                            features["spectral_centroid"] = np.mean(features["spectral_centroid"]).item() # Convert to scalar
                        else:
                            features["spectral_centroid"] = None

                    if "bandwidth" in feature_types:
                        # Bandwidth (simplified)
                        if f.size > 0:
                            features["bandwidth"] = (f[-1] - f[0]).item() # Total bandwidth
                        else:
                            features["bandwidth"] = None

                    extracted_features.append(features)
        
        return extracted_features

feature_extraction_service = FeatureExtractionService()


