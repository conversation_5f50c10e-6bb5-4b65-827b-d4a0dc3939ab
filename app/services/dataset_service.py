from typing import Dict, Any, List
import uuid
from datetime import datetime

from ..models.schemas import Dataset, CreateDatasetRequest

# In-memory storage for datasets (in production, use a database)
datasets_db: Dict[str, Dataset] = {}

class DatasetService:
    def create_dataset(self, dataset_data: CreateDatasetRequest) -> Dataset:
        dataset_id = str(uuid.uuid4())
        new_dataset = Dataset(
            id=dataset_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            **dataset_data.dict()
        )
        datasets_db[dataset_id] = new_dataset
        return new_dataset

    def get_dataset(self, dataset_id: str) -> Dataset:
        dataset = datasets_db.get(dataset_id)
        if not dataset:
            raise ValueError(f"Dataset with ID {dataset_id} not found")
        return dataset

    def list_datasets(self) -> List[Dataset]:
        return list(datasets_db.values())

    def update_dataset(self, dataset_id: str, update_data: Dict[str, Any]) -> Dataset:
        dataset = self.get_dataset(dataset_id)
        for key, value in update_data.items():
            setattr(dataset, key, value)
        dataset.updated_at = datetime.now()
        datasets_db[dataset_id] = dataset
        return dataset

    def delete_dataset(self, dataset_id: str):
        if dataset_id not in datasets_db:
            raise ValueError(f"Dataset with ID {dataset_id} not found")
        del datasets_db[dataset_id]

    def add_file_to_dataset(self, dataset_id: str, file_id: str) -> Dataset:
        dataset = self.get_dataset(dataset_id)
        if file_id not in dataset.file_ids:
            dataset.file_ids.append(file_id)
            dataset.updated_at = datetime.now()
            datasets_db[dataset_id] = dataset
        return dataset

    def add_annotation_to_dataset(self, dataset_id: str, annotation_id: str) -> Dataset:
        dataset = self.get_dataset(dataset_id)
        if annotation_id not in dataset.annotation_ids:
            dataset.annotation_ids.append(annotation_id)
            dataset.updated_at = datetime.now()
            datasets_db[dataset_id] = dataset
        return dataset

dataset_service = DatasetService()

