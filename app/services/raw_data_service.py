import os
import logging
import boto3
from botocore.exceptions import ClientError
from echolab2.instruments import EK80

from ..core.config import settings

logger = logging.getLogger(__name__)

class RawDataService:
    def __init__(self):
        self.local_raw_dir = settings.LOCAL_RAW_DIR
        if not os.path.exists(self.local_raw_dir):
            logger.info(f"Local raw data directory not found at {self.local_raw_dir}, creating it.")
            os.makedirs(self.local_raw_dir)
        self.s3_client = boto3.client("s3")

    def download_raw_file_from_s3(self, filename: str) -> str:
        """
        Télécharge un fichier .raw depuis S3 et le sauvegarde dans le répertoire local.
        Retourne le chemin local du fichier.
        """
        local_path = os.path.join(self.local_raw_dir, filename)
        s3_key = f"{settings.S3_PREFIX}/{filename}"
        
        logger.info(f"Downloading s3://{settings.S3_BUCKET_NAME}/{s3_key} to {local_path}")
        
        try:
            self.s3_client.download_file(settings.S3_BUCKET_NAME, s3_key, local_path)
            return local_path
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                logger.error(f"File not found on S3: {s3_key}")
                raise FileNotFoundError(f"File not found on S3: {filename}")
            else:
                logger.error(f"Error downloading from S3: {e}")
                raise ConnectionError(f"Failed to download file from S3: {filename}")

    def list_local_raw_files(self):
        """
        Liste tous les fichiers .raw présents dans le dossier local.
        """
        return [
            os.path.join(self.local_raw_dir, f)
            for f in os.listdir(self.local_raw_dir)
            if f.endswith(".raw")
        ]

    def process_raw_file(self, file_path: str):
        """
        Charge et lit un fichier .raw avec echolab2 (EK80).
        """
        try:
            logger.info(f"Processing raw file: {file_path}")
            ek80 = EK80.EK80()
            ek80.read_raw([file_path], max_sample_count=1000)
            if not ek80.channel_ids:
                logger.warning(f"No channels found in the raw file: {file_path}")
                raise ValueError("No channels found in the raw file")
            return ek80
        except Exception as e:
            logger.error(f"Failed to process raw file with echolab2: {file_path}", exc_info=True)
            raise RuntimeError(f"Failed to process raw file with echolab2: {str(e)}")

    def get_file_metadata(self, file_path: str) -> dict:
        """
        Extrait des métadonnées à partir d’un fichier .raw local.
        """
        try:
            logger.info(f"Extracting metadata from: {file_path}")
            ek80 = EK80.EK80()
            ek80.read_raw([file_path], max_sample_count=1)
            metadata = {
                "channel_ids": ek80.channel_ids,
                "ping_count": len(ek80.raw_data[ek80.channel_ids[0]]) if ek80.channel_ids else 0,
                "frequencies": {
                    ch_id: getattr(ek80.get_channel_data(ch_id), 'frequency', None)
                    for ch_id in ek80.channel_ids
                }
            }
            return metadata
        except Exception as e:
            logger.warning(f"Could not extract metadata from {file_path}: {e}")
            return {}

# Utilisation de l'instance
raw_data_service = RawDataService()