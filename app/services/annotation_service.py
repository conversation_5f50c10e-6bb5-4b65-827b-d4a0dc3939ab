from typing import Dict, Any, List
import uuid
from datetime import datetime

from ..models.schemas import Annotation, CreateAnnotationRequest

# In-memory storage for annotations (in production, use a database)
annotations_db: Dict[str, Annotation] = {}

class AnnotationService:
    def create_annotation(self, annotation_data: CreateAnnotationRequest) -> Annotation:
        annotation_id = str(uuid.uuid4())
        new_annotation = Annotation(
            id=annotation_id,
            created_at=datetime.now(),
            **annotation_data.dict()
        )
        annotations_db[annotation_id] = new_annotation
        return new_annotation

    def get_annotation(self, annotation_id: str) -> Annotation:
        annotation = annotations_db.get(annotation_id)
        if not annotation:
            raise ValueError(f"Annotation with ID {annotation_id} not found")
        return annotation

    def get_annotations_by_file_id(self, file_id: str) -> List[Annotation]:
        return [anno for anno in annotations_db.values() if anno.file_id == file_id]

    def update_annotation(self, annotation_id: str, update_data: Dict[str, Any]) -> Annotation:
        annotation = self.get_annotation(annotation_id)
        for key, value in update_data.items():
            setattr(annotation, key, value)
        annotations_db[annotation_id] = annotation
        return annotation

    def delete_annotation(self, annotation_id: str):
        if annotation_id not in annotations_db:
            raise ValueError(f"Annotation with ID {annotation_id} not found")
        del annotations_db[annotation_id]

annotation_service = AnnotationService()


