from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import uuid

from .api import data_management, visualization, feature_extraction, annotation, dataset, ml_models, analysis
from .core.config import settings
from .services.raw_data_service import raw_data_service
from .api.data_management import loaded_files

# Configure logging
logging.basicConfig(level=settings.LOG_LEVEL)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Marine Acoustic Analysis API",
    description="API pour l'analyse des données acoustiques marines avec ML",
    version="1.0.0"
)

@app.on_event("startup")
async def startup_event():
    """
    Au démarrage, scanne le répertoire local et prépare les fichiers pour le chargement paresseux.
    """
    logger.info("Application startup: Scanning for local raw files (Lazy Loading enabled).")
    try:
        local_files = raw_data_service.list_local_raw_files()
        logger.info(f"Found {len(local_files)} local files. They will be loaded on demand.")
        
        for file_path in local_files:
            filename = os.path.basename(file_path)
            file_id = str(uuid.uuid4())
            
            # On ne charge que les informations de base, le reste est en attente (None)
            loaded_files[file_id] = {
                "file_id": file_id,
                "filename": filename,
                "local_path": file_path,
                "ek80_data": None,  # Sera chargé à la demande
                "metadata": None,   # Sera chargé à la demande
                "source": "local"
            }
        logger.info("Local file scanning complete. Server is ready.")

    except Exception as e:
        logger.error(f"An error occurred during startup file scanning: {e}", exc_info=True)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"],
)

# Include all routers
app.include_router(data_management.router)
app.include_router(visualization.router)
app.include_router(feature_extraction.router)
app.include_router(annotation.router)
app.include_router(dataset.router)
app.include_router(ml_models.router)
app.include_router(analysis.router)

@app.get("/")
def read_root():
    return {
        "message": "Marine Acoustic Analysis API",
        "version": "1.0.0",
        "endpoints": {
            "data": "/data",
            "visualization": "/visualize",
            "features": "/features",
            "annotations": "/annotations",
            "datasets": "/datasets",
            "models": "/models",
            "analysis": "/analysis"
        }
    }

@app.get("/health")
def health_check():
    return {"status": "healthy"}
