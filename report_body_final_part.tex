% ===== CONTINUATION OF CHAPTER 6 =====

isation par une agence gouvernementale :
\begin{itemize}
\item Suivi en temps réel des captures
\item Détection des infractions aux quotas
\item Génération automatique de rapports
\end{itemize}

\section{Conclusion du Chapitre}

Les résultats obtenus démontrent sans équivoque l'efficacité et la pertinence de notre solution. Les modèles de classification atteignent un niveau de performance élevé, tandis que le système global offre des performances robustes et scalables.

L'évaluation utilisateur confirme l'adéquation de la solution avec les besoins du terrain et met en évidence l'impact opérationnel significatif en termes de gain de temps, de réduction des coûts et d'amélioration de la précision. Ces résultats valident notre approche et ouvrent la voie à une adoption à grande échelle.

% ===== CHAPITRE 7 : DISCUSSION =====
\chapter{Discussion}

\section{Introduction}

Ce chapitre propose une analyse critique des résultats obtenus, en les replaçant dans le contexte scientifique et technologique actuel. Nous discutons des implications de notre travail, de ses limites, et des leçons apprises tout au long du projet.

\section{Interprétation des Résultats}

\subsection{Performance de la Classification}

La précision de 93\% obtenue par notre modèle CNN est un résultat très encourageant, se situant au niveau des meilleures études publiées dans le domaine. Cette performance est attribuable à plusieurs facteurs :

\begin{enumerate}
\item \textbf{Qualité des données} : L'utilisation d'un dataset vaste et varié a permis au modèle de généraliser efficacement.
\item \textbf{Augmentation de données} : Les techniques d'augmentation spécifiques à l'acoustique ont amélioré la robustesse du modèle.
\item \textbf{Architecture CNN} : Le modèle CNN avec mécanisme d'attention a su capturer les features pertinentes des spectrogrammes.
\end{enumerate}

Cependant, la confusion résiduelle entre les anchois et les sardines souligne la complexité de la discrimination acoustique pour des espèces morphologiquement proches.

\subsection{Impact de la Solution Complète}

Au-delà de la classification, la valeur ajoutée de notre projet réside dans l'intégration d'un pipeline complet. La plateforme NOAA Dataset Explorer, développée en amont, a été un catalyseur essentiel, résolvant un problème fondamental d'accès aux données pour toute la communauté scientifique.

L'architecture microservices a prouvé sa pertinence, permettant un développement parallèle et une scalabilité adaptée aux différents besoins (traitement intensif vs requêtes légères).

\section{Comparaison avec l'État de l'Art}

Notre solution se distingue de l'état de l'art sur plusieurs points clés :

\begin{itemize}
\item \textbf{Approche Holistique} : Contrairement aux outils existants qui se concentrent sur une seule étape (e.g., Echoview pour l'analyse), notre solution couvre l'ensemble du workflow.
\item \textbf{Accessibilité} : En tant que solution web, elle est accessible depuis n'importe quel appareil, sans installation complexe, démocratisant l'accès à ces technologies.
\item \textbf{Intelligence Artificielle} : L'intégration native de modèles d'IA pour la classification est une innovation majeure par rapport aux solutions traditionnelles.
\end{itemize}

\section{Limites et Défis}

\subsection{Limites du Modèle}

\begin{itemize}
\item \textbf{Généralisation} : Le modèle a été entraîné principalement sur des données du Pacifique. Sa performance sur d'autres écosystèmes (e.g., Méditerranée) reste à évaluer.
\item \textbf{Nombre d'espèces} : La classification est limitée à 3 espèces principales. L'ajout de nouvelles espèces nécessitera un réentraînement conséquent.
\item \textbf{Explicabilité} : Bien que les cartes d'attention fournissent des indices, l'interprétabilité complète des décisions du CNN reste un défi.
\end{itemize}

\subsection{Limites Techniques}

\begin{itemize}
\item \textbf{Dépendance au Cloud} : La solution nécessite une connexion internet, ce qui peut être une contrainte pour une utilisation en mer.
\item \textbf{Coût de calcul} : L'entraînement des modèles reste coûteux en termes de ressources GPU.
\end{itemize}

\section{Leçons Apprises}

\subsection{Techniques}

\begin{itemize}
\item L'importance cruciale de l'optimisation mémoire et du traitement asynchrone pour les applications de données volumineuses.
\item La combinaison de Python pour le backend et de JavaScript/TypeScript pour le frontend offre une flexibilité et une performance remarquables.
\item Les Web Workers sont indispensables pour maintenir une interface utilisateur réactive lors de visualisations intensives.
\end{itemize}

\subsection{Gestion de Projet}

\begin{itemize}
\item L'approche agile a permis de s'adapter rapidement aux défis et aux découvertes en cours de projet.
\item La phase initiale d'immersion dans le domaine métier a été un investissement de temps extrêmement rentable.
\item Une communication constante avec les experts du domaine est essentielle pour s'assurer que la solution développée répond aux besoins réels.
\end{itemize}

\section{Conclusion du Chapitre}

La discussion des résultats met en lumière la contribution significative de notre projet tout en reconnaissant ses limites. Notre solution représente une avancée notable dans le domaine de l'acoustique marine, mais elle constitue également une base sur laquelle de futures améliorations pourront être construites. Les leçons apprises durant ce projet constituent une expérience précieuse pour nos futures carrières d'ingénieurs.

% ===== CHAPITRE 8 : CONCLUSION ET PERSPECTIVES =====
\chapter{Conclusion et Perspectives}

\section{Bilan du Projet}

Au terme de ces six mois de stage au sein de Proteyus, nous avons mené à bien le développement d'une solution complète et innovante pour la classification d'espèces marines à partir de signaux d'échosondeurs. Ce projet, parti d'une page blanche en raison de l'absence de solutions équivalentes, a abouti à une plateforme fonctionnelle, performante et validée par des experts du domaine.

Les objectifs fixés en début de stage ont été atteints, et pour certains, dépassés :
\begin{itemize}
\item \textbf{Plateforme d'accès aux données} : La création de NOAA Dataset Explorer a non seulement répondu à un besoin du projet, mais a aussi fourni un outil précieux à la communauté scientifique.
\item \textbf{Pipeline de traitement} : Un pipeline robuste et optimisé a été mis en place, capable de traiter des fichiers .raw volumineux de manière efficace.
\item \textbf{Modèles de classification} : Des modèles d'apprentissage profond performants ont été développés, atteignant une précision de 93\%, ce qui est compétitif avec l'état de l'art.
\item \textbf{Solution intégrée} : L'ensemble a été intégré dans une application web moderne et intuitive, démontrant la faisabilité d'une solution de bout en bout.
\end{itemize}

\section{Contributions Principales}

Les contributions de ce projet sont multiples :
\begin{itemize}
\item \textbf{Scientifique} : Contribution à l'amélioration des méthodes de classification acoustique et à la validation de l'approche par apprentissage profond.
\item \textbf{Technologique} : Développement d'une architecture logicielle moderne et scalable pour l'analyse de données acoustiques, et optimisation de bibliothèques existantes comme PyEcholab.
\item \textbf{Opérationnelle} : Création d'un outil qui réduit drastiquement le temps et le coût d'analyse, avec un impact direct sur la gestion durable des pêcheries.
\item \textbf{Sociétale} : En facilitant la surveillance des écosystèmes marins, ce projet contribue, à son échelle, aux efforts de préservation de la biodiversité et de lutte contre la surpêche.
\end{itemize}

\section{Perspectives d'Amélioration}

Ce projet ouvre de nombreuses perspectives d'évolution et d'amélioration.

\subsection{Améliorations Techniques}
\begin{itemize}
\item \textbf{Traitement en temps réel} : Adapter l'architecture pour une classification en temps réel à bord des navires, en optimisant les modèles pour des systèmes embarqués (Edge AI).
\item \textbf{Transfer Learning} : Utiliser des modèles pré-entraînés sur de vastes datasets acoustiques pour accélérer l'ajout de nouvelles espèces et améliorer la performance avec moins de données.
\item \textbf{Modèles multimodaux} : Intégrer d'autres sources de données (vidéo sous-marine, données satellites, paramètres océanographiques) pour une classification plus robuste.
\end{itemize}

\subsection{Évolutions Fonctionnelles}
\begin{itemize}
\item \textbf{Estimation de la biomasse} : Développer des modules pour estimer la taille et le poids des bancs de poissons, une information cruciale pour les gestionnaires de pêcheries.
\item \textbf{Analyse comportementale} : Utiliser des modèles de type LSTM ou Transformers pour analyser les séquences d'échogrammes et détecter des comportements spécifiques (reproduction, migration).
\item \textbf{API publique} : Ouvrir une API pour permettre à des tiers d'intégrer nos services de classification dans leurs propres applications.
\end{itemize}

\subsection{Applications Industrielles}
\begin{itemize}
\item \textbf{Partenariats institutionnels} : Collaborer avec des instituts de recherche marine (comme l'IFREMER en France) pour déployer la solution à grande échelle.
\item \textbf{Intégration matérielle} : Travailler avec des fabricants d'échosondeurs comme Kongsberg pour intégrer la solution directement dans leurs systèmes.
\item \textbf{Diversification} : Adapter la technologie pour d'autres applications, comme la surveillance des mammifères marins, la détection de pollution ou l'inspection d'infrastructures sous-marines.
\end{itemize}

\section{Conclusion Finale}

Ce projet de fin d'études a été une expérience extrêmement formatrice, nous plongeant au cœur des défis technologiques et scientifiques du monde de l'acoustique marine. Il nous a permis de mobiliser l'ensemble des compétences acquises durant notre cursus d'ingénieur, de la conception logicielle à l'intelligence artificielle, en passant par la gestion de projet.

Le développement de cette plateforme de classification acoustique n'est pas seulement un accomplissement technique ; c'est une démonstration du potentiel de l'intelligence artificielle pour répondre à des enjeux environnementaux critiques. Nous sommes fiers d'avoir contribué à un projet porteur de sens et sommes convaincus que les technologies développées durant ce stage ont un avenir prometteur au sein de Proteyus et dans l'industrie maritime au sens large.

% ===== BIBLIOGRAPHIE =====
\begin{thebibliography}{99}
\addcontentsline{toc}{chapter}{Bibliographie}

\bibitem{simmonds2005}
Simmonds, J., \& MacLennan, D. N. (2005). \textit{Fisheries Acoustics: Theory and Practice}. Blackwell Science.

\bibitem{urick1983}
Urick, R. J. (1983). \textit{Principles of Underwater Sound}. McGraw-Hill.

\bibitem{francois1982}
Francois, R. E., \& Garrison, G. R. (1982). Sound absorption based on ocean measurements. Part II: Boric acid contribution and equation for total absorption. \textit{The Journal of the Acoustical Society of America}, 72(6), 1879-1890.

\bibitem{lecun2015}
LeCun, Y., Bengio, Y., \& Hinton, G. (2015). Deep learning. \textit{Nature}, 521(7553), 436-444.

\bibitem{krizhevsky2012}
Krizhevsky, A., Sutskever, I., \& Hinton, G. E. (2012). ImageNet classification with deep convolutional neural networks. \textit{Advances in neural information processing systems}, 25.

\bibitem{echoview}
Echoview Software Pty Ltd. (2025). \textit{Echoview User Guide}. Hobart, Australia.

\bibitem{pyecholab}
NOAA Alaska Fisheries Science Center. (2020). \textit{PyEcholab: A Python library for fisheries acoustics}. GitHub Repository.

\bibitem{echopype}
The Echopype Development Team. (2022). \textit{Echopype: A Python library for interoperable processing of fisheries acoustics data}. GitHub Repository.

\bibitem{fastapi}
Tiangolo. (2024). \textit{FastAPI Documentation}. \url{https://fastapi.tiangolo.com/}

\bibitem{reactjs}
Meta Platforms, Inc. (2024). \textit{React Documentation}. \url{https://react.dev/}

\bibitem{flyio}
Fly.io. (2024). \textit{Fly.io Documentation}. \url{https://fly.io/docs/}

\end{thebibliography}

% ===== ANNEXES =====
\appendix
\chapter{Annexes}
\addcontentsline{toc}{chapter}{Annexes}

\section{Annexe A : Diagramme de Gantt du Projet}
\begin{figure}[h!]
\centering
\includegraphics[width=\textwidth]{gantt_chart_placeholder.png}
\caption{Diagramme de Gantt prévisionnel du projet.}
\label{fig:gantt_chart}
\end{figure}

\section{Annexe B : Exemples de Visualisations}
\begin{figure}[h!]
\centering
\includegraphics[width=0.8\textwidth]{echogram_placeholder.png}
\caption{Exemple d'échogramme multi-fréquences généré par la plateforme.}
\label{fig:echogram_example}
\end{figure}

\begin{figure}[h!]
\centering
\includegraphics[width=0.8\textwidth]{spectrogram_placeholder.png}
\caption{Spectrogramme d'un signal acoustique utilisé pour l'entraînement du CNN.}
\label{fig:spectrogram_example}
\end{figure}

\section{Annexe C : Interface Utilisateur}
\begin{figure}[h!]
\centering
\includegraphics[width=\textwidth]{ui_screenshot_placeholder.png}
\caption{Capture d'écran de l'interface principale de l'application web.}
\label{fig:ui_screenshot}
\end{figure}

\end{content}