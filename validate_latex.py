#!/usr/bin/env python3
"""
LaTeX validation script to check for common errors
"""

import re
import sys

def check_latex_file(filename):
    """Check LaTeX file for common errors"""
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    errors = []
    
    # Track environments
    env_stack = []
    table_count = 0
    figure_count = 0
    lstlisting_count = 0
    
    for i, line in enumerate(lines, 1):
        # Check for begin/end matching
        begin_match = re.search(r'\\begin\{(\w+)\}', line)
        if begin_match:
            env = begin_match.group(1)
            env_stack.append((env, i))
            if env == 'table':
                table_count += 1
            elif env == 'figure':
                figure_count += 1
            elif env == 'lstlisting':
                lstlisting_count += 1
        
        end_match = re.search(r'\\end\{(\w+)\}', line)
        if end_match:
            env = end_match.group(1)
            if env == 'table':
                table_count -= 1
            elif env == 'figure':
                figure_count -= 1
            elif env == 'lstlisting':
                lstlisting_count -= 1
                
            if env_stack and env_stack[-1][0] == env:
                env_stack.pop()
            else:
                errors.append(f"Line {i}: Mismatched \\end{{{env}}}")
    
    # Check for unclosed environments
    for env, line_num in env_stack:
        errors.append(f"Line {line_num}: Unclosed \\begin{{{env}}}")
    
    # Check for unmatched braces
    brace_count = 0
    for i, line in enumerate(lines, 1):
        # Skip comments
        comment_pos = line.find('%')
        if comment_pos >= 0:
            line = line[:comment_pos]
        
        for char in line:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
        
        if brace_count < 0:
            errors.append(f"Line {i}: Extra closing brace")
            brace_count = 0
    
    if brace_count > 0:
        errors.append(f"Missing {brace_count} closing brace(s)")
    
    # Check for common LaTeX errors
    for i, line in enumerate(lines, 1):
        # Check for & outside of tables
        if '&' in line and not any(env in ['table', 'tabular', 'tabularx', 'longtable'] 
                                   for env, _ in env_stack):
            if not line.strip().startswith('%'):
                errors.append(f"Line {i}: & character outside table environment")
        
        # Check for $ math mode errors
        dollar_count = line.count('$') - line.count('\\$')
        if dollar_count % 2 != 0:
            errors.append(f"Line {i}: Odd number of $ characters")
    
    return errors

if __name__ == "__main__":
    filename = sys.argv[1] if len(sys.argv) > 1 else "report.tex"
    
    print(f"Checking {filename} for LaTeX errors...")
    errors = check_latex_file(filename)
    
    if errors:
        print(f"\nFound {len(errors)} error(s):")
        for error in errors:
            print(f"  - {error}")
        sys.exit(1)
    else:
        print("\nNo errors found! The file should compile correctly.")
        sys.exit(0)
