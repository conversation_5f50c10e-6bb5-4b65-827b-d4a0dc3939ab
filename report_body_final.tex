% ===== CONTINUATION OF CHAPTER 4 =====

/build
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Stage 2: Runtime
FROM python:3.9-slim
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgeos-dev \
    libproj-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.9/site-packages /usr/local/lib/python3.9/site-packages

# Copy application
COPY . .

# Non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
\end{lstlisting}

\subsection{Orchestration Kubernetes}

\begin{lstlisting}[language=yaml, caption=Déploiement Kubernetes]
apiVersion: apps/v1
kind: Deployment
metadata:
  name: acoustic-classifier
spec:
  replicas: 3
  selector:
    matchLabels:
      app: acoustic-classifier
  template:
    metadata:
      labels:
        app: acoustic-classifier
    spec:
      containers:
      - name: api
        image: proteyus/acoustic-api:latest
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: acoustic-classifier-service
spec:
  selector:
    app: acoustic-classifier
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
\end{lstlisting}

\subsection{Déploiement sur Fly.io}

Configuration optimisée pour Fly.io :

\begin{lstlisting}[caption=Configuration fly.toml]
app = "noaa-acoustic-classifier"

[env]
  PORT = "8080"
  PYTHON_ENV = "production"

[experimental]
  allowed_public_ports = []
  auto_rollback = true

[[services]]
  http_checks = []
  internal_port = 8080
  protocol = "tcp"
  script_checks = []

  [services.concurrency]
    hard_limit = 25
    soft_limit = 20
    type = "connections"

  [[services.ports]]
    force_https = true
    handlers = ["http"]
    port = 80

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

  [[services.tcp_checks]]
    grace_period = "1s"
    interval = "15s"
    restart_limit = 0
    timeout = "2s"

[mounts]
  destination = "/data"
  source = "acoustic_data"
\end{lstlisting}

\section{Outils de Développement et Qualité}

\subsection{Pipeline CI/CD}

\begin{lstlisting}[language=yaml, caption=GitHub Actions workflow]
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml
    
    - name: Static analysis
      run: |
        flake8 app/
        mypy app/
        black --check app/
    
    - name: Security scan
      run: |
        bandit -r app/
        safety check
    
  build-and-deploy:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: docker build -t ${{ secrets.REGISTRY }}/acoustic-api:${{ github.sha }} .
    
    - name: Push to registry
      run: |
        echo ${{ secrets.REGISTRY_PASSWORD }} | docker login -u ${{ secrets.REGISTRY_USERNAME }} --password-stdin
        docker push ${{ secrets.REGISTRY }}/acoustic-api:${{ github.sha }}
    
    - name: Deploy to Fly.io
      run: |
        flyctl deploy --image ${{ secrets.REGISTRY }}/acoustic-api:${{ github.sha }}
\end{lstlisting}

\subsection{Monitoring et Observabilité}

\begin{lstlisting}[language=Python, caption=Instrumentation avec Prometheus]
from prometheus_client import Counter, Histogram, Gauge
import time

# Métriques personnalisées
classification_counter = Counter(
    'classification_total', 
    'Total classifications', 
    ['species', 'model_version']
)

classification_duration = Histogram(
    'classification_duration_seconds',
    'Classification duration'
)

active_connections = Gauge(
    'websocket_connections_active',
    'Active WebSocket connections'
)

# Middleware de monitoring
@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    REQUEST_DURATION.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).observe(duration)
    
    return response
\end{lstlisting}

\section{Sécurité et Performance}

\subsection{Sécurité API}

\begin{lstlisting}[language=Python, caption=Implémentation sécurité JWT]
from jose import JWTError, jwt
from passlib.context import CryptContext
from datetime import datetime, timedelta

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class SecurityManager:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
    
    def create_access_token(self, data: dict, expires_delta: timedelta = None):
        to_encode = data.copy()
        expire = datetime.utcnow() + (expires_delta or timedelta(minutes=15))
        to_encode.update({"exp": expire})
        
        encoded_jwt = jwt.encode(
            to_encode, 
            self.secret_key, 
            algorithm=self.algorithm
        )
        return encoded_jwt
    
    async def get_current_user(self, token: str = Depends(oauth2_scheme)):
        credentials_exception = HTTPException(
            status_code=401,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm]
            )
            username: str = payload.get("sub")
            if username is None:
                raise credentials_exception
        except JWTError:
            raise credentials_exception
        
        user = await get_user(username=username)
        if user is None:
            raise credentials_exception
        
        return user
\end{lstlisting}

\subsection{Optimisation des Performances}

\begin{table}[h]
\centering
\begin{tabular}{|l|l|l|}
\hline
\textbf{Technique} & \textbf{Implémentation} & \textbf{Gain} \\
\hline
Caching & Redis + CDN & 85\% hit rate \\
Connection pooling & asyncpg pool & 3× throughput \\
Compression & Brotli + gzip & 70\% bandwidth \\
Lazy loading & React Suspense & 2× initial load \\
Code splitting & Webpack chunks & 60\% bundle size \\
\hline
\end{tabular}
\caption{Techniques d'optimisation appliquées}
\label{tab:optimization_techniques}
\end{table}

\section{Conclusion du Chapitre}

Le choix judicieux des technologies et leur intégration harmonieuse constituent la fondation technique de notre solution. L'adoption de technologies modernes comme FastAPI, React 18, et TensorFlow 2.x, combinée à des optimisations spécifiques au domaine acoustique, nous permet d'atteindre des performances exceptionnelles tout en maintenant une architecture maintenable et évolutive.

L'infrastructure cloud-native garantit la scalabilité nécessaire pour traiter les volumes massifs de données acoustiques, tandis que les pratiques DevOps assurent la qualité et la fiabilité du système en production.

% ===== CHAPITRE 5 : DÉVELOPPEMENT DE LA SOLUTION =====
\chapter{Développement de la Solution}

\section{Introduction}

Ce chapitre détaille la conception et l'implémentation de notre solution de classification acoustique marine. Nous présentons l'architecture complète, les algorithmes développés, et les défis techniques surmontés durant la phase de réalisation.

\section{Architecture Backend}

\subsection{Design du Pipeline de Traitement}

Le pipeline de traitement des données acoustiques suit une architecture en étapes distinctes :

\begin{figure}[h]
\centering
\begin{tikzpicture}[node distance=2cm, scale=0.9, every node/.style={scale=0.9}]
% Nodes
\node (input) [rectangle, draw, fill=blue!20] {Fichier .raw};
\node (parse) [rectangle, draw, fill=green!20, below of=input] {Parsing};
\node (calib) [rectangle, draw, fill=green!20, below of=parse] {Calibration};
\node (process) [rectangle, draw, fill=yellow!20, below of=calib] {Processing};
\node (feature) [rectangle, draw, fill=yellow!20, right of=process, xshift=2cm] {Feature Extraction};
\node (classify) [rectangle, draw, fill=red!20, above of=feature] {Classification};
\node (output) [rectangle, draw, fill=gray!20, above of=classify] {Résultats};

% Arrows
\draw[->] (input) -- node[right] {Binary data} (parse);
\draw[->] (parse) -- node[right] {Raw samples} (calib);
\draw[->] (calib) -- node[right] {Sv values} (process);
\draw[->] (process) -- node[above] {Echogram} (feature);
\draw[->] (feature) -- node[right] {Features} (classify);
\draw[->] (classify) -- node[right] {Predictions} (output);
\end{tikzpicture}
\caption{Pipeline de traitement des données}
\label{fig:processing_pipeline}
\end{figure}

\subsection{Service de Parsing des Fichiers .raw}

\subsubsection{Analyse de la Structure Binaire}

Les fichiers .raw suivent une structure complexe que nous avons dû décoder :

\begin{lstlisting}[language=Python, caption=Parser de fichiers .raw optimisé]
class RawFileParser:
    def __init__(self, filename: str):
        self.filename = filename
        self.datagrams = []
        self.config = {}
        
    async def parse(self):
        """Parse asynchrone avec validation CRC"""
        async with aiofiles.open(self.filename, 'rb') as f:
            # Lecture de l'en-tête
            header = await f.read(12)
            if header[:4] != b'CON0':
                raise ValueError("Invalid file format")
            
            file_size = struct.unpack('<I', header[4:8])[0]
            
            # Parsing des datagrammes
            while True:
                datagram_header = await f.read(12)
                if not datagram_header:
                    break
                
                dg_type = datagram_header[:4].decode('ascii')
                dg_size = struct.unpack('<I', datagram_header[4:8])[0]
                timestamp = struct.unpack('<Q', datagram_header[8:16])[0]
                
                # Lecture du contenu
                content = await f.read(dg_size - 12)
                
                # Vérification CRC
                crc_read = struct.unpack('<I', await f.read(4))[0]
                crc_calc = zlib.crc32(datagram_header + content)
                
                if crc_read != crc_calc:
                    logger.warning(f"CRC mismatch at offset {f.tell()}")
                    continue
                
                # Traitement selon le type
                if dg_type == 'RAW3':
                    samples = await self._parse_raw3(content)
                    self.datagrams.append({
                        'type': 'samples',
                        'timestamp': timestamp,
                        'data': samples
                    })
                elif dg_type == 'NME0':
                    nmea = await self._parse_nmea(content)
                    self.datagrams.append({
                        'type': 'navigation',
                        'timestamp': timestamp,
                        'data': nmea
                    })
    
    async def _parse_raw3(self, data: bytes) -> np.ndarray:
        """Parse RAW3 datagram (acoustic samples)"""
        # Structure: channel, offset, count, samples
        channel = struct.unpack('<H', data[0:2])[0]
        offset = struct.unpack('<I', data[2:6])[0]
        count = struct.unpack('<I', data[6:10])[0]
        
        # Décompression si nécessaire
        if data[10] == 0x01:  # Compressed
            samples = zlib.decompress(data[11:])
        else:
            samples = data[11:]
        
        # Conversion en array numpy
        dtype = np.complex64 if channel > 100000 else np.float32
        return np.frombuffer(samples, dtype=dtype).reshape(-1, count)
\end{lstlisting}

\subsection{Service de Calibration et Correction}

\subsubsection{Implémentation de l'Équation du Sonar}

\begin{lstlisting}[language=Python, caption=Calibration acoustique complète]
class AcousticCalibrator:
    def __init__(self, config: CalibrationConfig):
        self.config = config
        
    def calibrate_samples(self, raw_samples: np.ndarray, metadata: dict) -> np.ndarray:
        """Applique la calibration complète aux échantillons"""
        
        # 1. Conversion en puissance
        power = np.abs(raw_samples) ** 2
        
        # 2. Calcul du TS (Target Strength)
        ts = 10 * np.log10(power) - self.config.system_gain
        
        # 3. Correction TVG (Time Varied Gain)
        range_m = metadata['range']
        absorption = self._calculate_absorption(
            metadata['frequency'],
            metadata['temperature'],
            metadata['salinity'],
            metadata['depth']
        )
        
        tvg = 20 * np.log10(range_m) + 2 * absorption * range_m
        
        # 4. Calcul Sv (Volume backscattering strength)
        sv = ts + tvg - 10 * np.log10(
            self._calculate_sample_volume(
                range_m,
                metadata['pulse_length'],
                metadata['beam_width']
            )
        )
        
        return sv
    
    def _calculate_absorption(self, freq_khz, temp_c, salinity_ppt, depth_m):
        """Calcul de l'absorption selon Francois & Garrison (1982)"""
        # Fréquences de relaxation
        f1 = 2.8 * np.sqrt(salinity_ppt / 35) * 10 ** (4 - 1245 / (temp_c + 273))
        f2 = 8.17 * 10 ** (8 - 1990 / (temp_c + 273)) / (1 + 0.0018 * (salinity_ppt - 35))
        
        # Pression en fonction de la profondeur
        p = 1 + depth_m / 10
        
        # Contributions à l'absorption
        a1 = 8.686 / (freq_khz ** 2) * (
            0.106 * f1 * freq_khz ** 2 / (f1 ** 2 + freq_khz ** 2) * np.exp((pH - 8) / 0.56)
        )
        
        a2 = 8.686 / (freq_khz ** 2) * (
            0.52 * (1 + temp_c / 43) * (salinity_ppt / 35) * 
            f2 * freq_khz ** 2 / (f2 ** 2 + freq_khz ** 2) * np.exp(-depth_m / 6000)
        )
        
        a3 = 8.686 / (freq_khz ** 2) * (
            4.9e-4 * freq_khz ** 2 * np.exp(-(temp_c / 27 + depth_m / 17000))
        )
        
        return (a1 + a2 + a3) * p
\end{lstlisting}

\subsection{Service de Génération d'Échogrammes}

\subsubsection{Algorithme de Rendu Optimisé}

\begin{lstlisting}[language=Python, caption=Génération d'échogrammes haute performance]
class EchogramGenerator:
    def __init__(self, colormap='viridis'):
        self.colormap = plt.get_cmap(colormap)
        self.cache = {}
        
    async def generate_echogram(
        self, 
        sv_data: np.ndarray, 
        time_axis: np.ndarray,
        depth_axis: np.ndarray,
        options: EchogramOptions
    ) -> np.ndarray:
        """Génère un échogramme avec options avancées"""
        
        # Vérification du cache
        cache_key = hashlib.md5(
            sv_data.tobytes() + str(options).encode()
        ).hexdigest()
        
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # Prétraitement
        sv_filtered = await self._apply_filters(sv_data, options)
        
        # Interpolation pour affichage uniforme
        if options.interpolation:
            from scipy.interpolate import RectBivariateSpline
            
            interp = RectBivariateSpline(
                time_axis, 
                depth_axis, 
                sv_filtered,
                kx=1, ky=1
            )
            
            # Grille uniforme
            time_new = np.linspace(time_axis[0], time_axis[-1], options.width)
            depth_new = np.linspace(depth_axis[0], depth_axis[-1], options.height)
            
            sv_interp = interp(time_new, depth_new)
        else:
            sv_interp = sv_filtered
        
        # Normalisation et coloration
        sv_norm = np.clip(
            (sv_interp - options.threshold_min) / 
            (options.threshold_max - options.threshold_min),
            0, 1
        )
        
        # Application de la colormap
        echogram_rgba = self.colormap(sv_norm)
        
        # Conversion en image
        echogram_img = (echogram_rgba[:, :, :3] * 255).astype(np.uint8)
        
        # Ajout d'overlays si demandé
        if options.show_bottom:
            echogram_img = await self._add_bottom_detection(echogram_img, sv_data)
        
        if options.show_schools:
            echogram_img = await self._add_school_detection(echogram_img, sv_data)
        
        # Cache du résultat
        self.cache[cache_key] = echogram_img
        
        return echogram_img
    
    async def _apply_filters(self, sv_data: np.ndarray, options: EchogramOptions):
        """Application de filtres de prétraitement"""
        result = sv_data.copy()
        
        # Filtre médian pour réduire le bruit
        if options.median_filter:
            from scipy.ndimage import median_filter
            result = median_filter(result, size=options.median_filter_size)
        
        # Filtre de convolution pour lisser
        if options.smooth:
            kernel = np.ones((3, 3)) / 9
            result = scipy.signal.convolve2d(result, kernel, mode='same')
        
        # Suppression du bruit de fond
        if options.remove_background:
            background = np.percentile(result, 10, axis=0)
            result = result - background[np.newaxis, :]
        
        return result
\end{lstlisting}

\section{Développement Frontend}

\subsection{Architecture React Modulaire}

\subsubsection{Structure des Composants}

\begin{lstlisting}[language=TypeScript, caption=Architecture des composants React]
// Structure du projet
src/
├── components/
│   ├── common/
│   │   ├── Layout.tsx
│   │   ├── Navigation.tsx
│   │   └── LoadingSpinner.tsx
│   ├── echogram/
│   │   ├── EchogramViewer.tsx
│   │   ├── EchogramControls.tsx
│   │   └── ColorBar.tsx
│   ├── classification/
│   │   ├── ClassificationPanel.tsx
│   │   ├── SpeciesSelector.tsx
│   │   └── ResultsDisplay.tsx
│   └── data/
│       ├── FileUploader.tsx
│       ├── DatasetBrowser.tsx
│       └── MetadataViewer.tsx
├── hooks/
│   ├── useEchogramData.ts
│   ├── useWebSocket.ts
│   └── useClassification.ts
├── services/
│   ├── api.ts
│   ├── websocket.ts
│   └── storage.ts
└── utils/
    ├── echogramProcessing.ts
    ├── colorMaps.ts
    └── validation.ts
\end{lstlisting}

\subsubsection{Composant Principal d'Affichage}

\begin{lstlisting}[language=TypeScript, caption=Composant EchogramViewer avancé]
interface EchogramViewerProps {
    fileId: string;
    frequency: number;
    onRegionSelect?: (region: Region) => void;
}

export const EchogramViewer: React.FC<EchogramViewerProps> = ({
    fileId,
    frequency,
    onRegionSelect
}) => {
    const [echogramData, setEchogramData] = useState<EchogramData | null>(null);
    const [zoom, setZoom] = useState({ x: 1, y: 1 });
    const [pan, setPan] = useState({ x: 0, y: 0 });
    const [selection, setSelection] = useState<Region | null>(null);
    
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    
    // Hook personnalisé pour les données
    const { data, loading, error } = useEchogramData(fileId, frequency);
    
    // Rendu WebGL pour performance
    useEffect(() => {
        if (!data || !canvasRef.current) return;
        
        const renderer = new WebGLEchogramRenderer(canvasRef.current);
        renderer.render(data, {
            zoom,
            pan,
            colorMap: 'viridis',
            threshold: { min: -80, max: -20 }
        });
        
        return () => renderer.dispose();
    }, [data, zoom, pan]);
    
    // Gestion des interactions
    const handleWheel = useCallback((e: WheelEvent) => {
        e.preventDefault();
        const delta = e.deltaY > 0 ? 0.9 : 1.1;
        setZoom(prev => ({
            x: Math.max(1, Math.min(10, prev.x * delta)),
            y: Math.max(1, Math.min(10, prev.y * delta))
        }));
    }, []);
    
    const handleMouseDown = useCallback((e: MouseEvent) => {
        const startX = e.clientX;
        const startY = e.clientY;
        const startPan = { ...pan };
        
        const handleMouseMove = (e: MouseEvent) => {
            setPan({
                x: startPan.x + (e.clientX - startX) / zoom.x,
                y: startPan.y + (e.clientY - startY) / zoom.y
            });
        };
        
        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
        
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }, [pan, zoom]);
    
    // Interface de sélection de région
    const handleRegionSelection = useCallback((e: MouseEvent) => {
        if (!e.shiftKey) return;
        
        const rect = canvasRef.current!.getBoundingClientRect();
        const x = (e.clientX - rect.left) / zoom.x - pan.x;
        const y = (e.clientY - rect.top) / zoom.y - pan.y;
        
        if (!selection) {
            setSelection({ x1: x, y1: y, x2: x, y2: y });
        } else {
            const region = {
                x1: Math.min(selection.x1, x),
                y1: Math.min(selection.y1, y),
                x2: Math.max(selection.x1, x),
                y2: Math.max(selection.y1, y)
            };
            setSelection(region);
            onRegionSelect?.(region);
        }
    }, [selection, zoom, pan, onRegionSelect]);
    
    return (
        <div className="echogram-container" ref={containerRef}>
            {loading && <LoadingOverlay />}
            {error && <ErrorMessage error={error} />}
            
            <canvas
                ref={canvasRef}
                className="echogram-canvas"
                onWheel={handleWheel}
                onMouseDown={handleMouseDown}
                onClick={handleRegionSelection}
            />
            
            <EchogramControls
                zoom={zoom}
                onZoomChange={setZoom}
                onResetView={() => {
                    setZoom({ x: 1, y: 1 });
                    setPan({ x: 0, y: 0 });
                }}
            />
            
            <ColorBar
                min={-80}
                max={-20}
                unit="dB"
                colorMap="viridis"
            />
            
            {selection && (
                <SelectionOverlay
                    selection={selection}
                    onClear={() => setSelection(null)}
                />
            )}
        </div>
    );
};
\end{lstlisting}

\subsection{Intégration WebSocket pour Temps Réel}

\begin{lstlisting}[language=TypeScript, caption=Hook WebSocket personnalisé]
export const useWebSocket = (url: string) => {
    const [socket, setSocket] = useState<WebSocket | null>(null);
    const [isConnected, setIsConnected] = useState(false);
    const [lastMessage, setLastMessage] = useState<any>(null);
    
    const messageHandlers = useRef<Map<string, (data: any) => void>>(new Map());
    
    useEffect(() => {
        const ws = new WebSocket(url);
        
        ws.onopen = () => {
            setIsConnected(true);
            console.log('WebSocket connected');
        };
        
        ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            setLastMessage(message);
            
            // Dispatch to specific handlers
            const handler = messageHandlers.current.get(message.type);
            if (handler) {
                handler(message.data);
            }
        };
        
        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
        
        ws.onclose = () => {
            setIsConnected(false);
            // Reconnection logic
            setTimeout(() => {
                setSocket(new WebSocket(url));
            }, 5000);
        };
        
        setSocket(ws);
        
        return () => {
            ws.close();
        };
    }, [url]);
    
    const subscribe = useCallback((messageType: string, handler: (data: any) => void) => {
        messageHandlers.current.set(messageType, handler);
        
        return () => {
            messageHandlers.current.delete(messageType);
        };
    }, []);
    
    const send = useCallback((type: string, data: any) => {
        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({ type, data }));
        }
    }, [socket]);
    
    return {
        isConnected,
        lastMessage,
        subscribe,
        send
    };
};
\end{lstlisting}

\section{Implémentation des Modèles d'IA}

\subsection{Préparation des Données}

\subsubsection{Pipeline de Prétraitement}

\begin{lstlisting}[language=Python, caption=Pipeline de préparation des données]
class DataPreprocessor:
    def __init__(self, config: PreprocessConfig):
        self.config = config
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
    def prepare_dataset(self, raw_files: List[str], labels: List[str]):
        """Prépare le dataset complet pour l'entraînement"""
        
        X, y = [], []
        
        for file_path, label in tqdm(zip(raw_files, labels), desc="Processing files"):
            try:
                # Extraction des features
                features = self.extract_features(file_path)
                
                # Augmentation des données
                augmented = self.augment_data(features)
                
                X.extend(augmented)
                y.extend([label] * len(augmented))
                
            except Exception as e:
                logger