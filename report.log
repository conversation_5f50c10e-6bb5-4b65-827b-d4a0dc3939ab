This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.8)  7 AUG 2025 02:24
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**report.tex
(./report.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/base/report.cls
Document Class: report 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@chapter=\count197
\c@section=\count198
\c@subsection=\count199
\c@subsubsection=\count266
\c@paragraph=\count267
\c@subparagraph=\count268
\c@figure=\count269
\c@table=\count270
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/02/14 v25.4 The multilingual framework for pdfLaTeX, LuaLaT
eX and XeLaTeX
\babel@savecnt=\count271
\U@D=\dimen142
\l@unhyphenated=\language23

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count272

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/babel-french/french.ldf
Language: french 2024-07-25 v3.6c French support from the babel system
Package babel Info: Hyphen rules for 'acadian' set to \l@french
(babel)             (\language11). Reported on input line 91.
Package babel Info: Hyphen rules for 'canadien' set to \l@french
(babel)             (\language11). Reported on input line 92.
\FB@stdchar=\count273
Package babel Info: Making : an active character on input line 421.
Package babel Info: Making ; an active character on input line 422.
Package babel Info: Making ! an active character on input line 423.
Package babel Info: Making ? an active character on input line 424.
\FBguill@level=\count274
\FBold@everypar=\toks19
\FB@Mht=\dimen143
\mc@charclass=\count275
\mc@charfam=\count276
\mc@charslot=\count277
\std@mcc=\count278
\dec@mcc=\count279
\FB@parskip=\dimen144
\listindentFB=\dimen145
\descindentFB=\dimen146
\labelindentFB=\dimen147
\labelwidthFB=\dimen148
\leftmarginFB=\dimen149
\parindentFFN=\dimen150
\FBfnindent=\dimen151
))
(/usr/local/texlive/2025basic/texmf-dist/tex/generic/babel/locale/fr/babel-fren
ch.tex
Package babel Info: Importing font and identification data for french
(babel)             from babel-fr.ini. Reported on input line 11.
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/carlisle/scalefnt.sty)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(/usr/local/texlive/2025basic/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen152
\Gin@req@width=\dimen153
)
(/usr/local/texlive/2025basic/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/local/texlive/2025basic/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count280
\Gm@cntv=\count281
\c@Gm@tempcnt=\count282
\Gm@bindingoffset=\dimen154
\Gm@wd@mp=\dimen155
\Gm@odd@mp=\dimen156
\Gm@even@mp=\dimen157
\Gm@layoutwidth=\dimen158
\Gm@layoutheight=\dimen159
\Gm@layouthoffset=\dimen160
\Gm@layoutvoffset=\dimen161
\Gm@dimlist=\toks21
)

! LaTeX Error: File `titlesec.sty' not found.

Type X to quit or <RETURN> to proceed,
or enter new name. (Default extension: sty)

Enter file name: X

! Emergency stop.
<read *> 
         
l.8 \usepackage
               {xcolor}
*** (cannot \read from terminal in nonstop modes)

 
Here is how much of TeX's memory you used:
 3402 strings out of 474662
 56848 string characters out of 5751110
 461780 words of memory out of 5000000
 26475 multiletter control sequences out of 15000+600000
 559899 words of font info for 38 fonts, out of 8000000 for 9000
 319 hyphenation exceptions out of 8191
 57i,0n,65p,204b,428s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
