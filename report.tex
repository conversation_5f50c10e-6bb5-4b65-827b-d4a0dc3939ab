% !TEX root = report.tex


\documentclass[12pt, a4paper]{report}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{titlesec}
\usepackage{xcolor}
\usepackage{ulem}
\usepackage{setspace}
\usepackage[colorlinks=true, linkcolor=black!70, urlcolor=black!70, citecolor=black!70]{hyperref}

\usepackage{listings}
\usepackage{tabularx}
\usepackage{float}
\usepackage{caption}
\usepackage{enumitem}
\usepackage{mdframed}
\usepackage{booktabs}
\usepackage{tikz}
\usepackage{lmodern}
\usepackage{amsmath}
\usepackage{longtable}
\usepackage{multirow}
\usepackage{afterpage}
\usepackage{pdflscape}
\usepackage{calc} % For precise calculations
\usepackage{amssymb} % For checkmark and cross symbols

% Add this to your preamble
\usepackage[export]{adjustbox} % For better image positioning
% Custom Arabic image command with size parameters
\newcommand{\arabicphrase}[2][0.3]{%
  \centering\includegraphics[
    width=#1\textwidth,
    height=2.5em,
    keepaspectratio
  ]{#2}\par
}

% Page layout and styling
\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}
\definecolor{proteyusblue}{RGB}{0,82,155}
\titleformat{\chapter}[display]{\normalfont\huge\bfseries\color{proteyusblue}}{\chaptertitlename\ \thechapter}{20pt}{\Huge}
\titleformat{\section}{\normalfont\Large\bfseries\color{proteyusblue}}{\thesection}{1em}{}
\onehalfspacing

% Code listing style
\lstdefinelanguage{JavaScript}{
  keywords={typeof, new, true, false, catch, function, return, null, catch, switch, var, if, in, while, do, else, case, break},
  keywordstyle=\color{blue}\bfseries,
  ndkeywords={class, export, boolean, throw, implements, import, this},
  ndkeywordstyle=\color{darkgray}\bfseries,
  identifierstyle=\color{black},
  sensitive=false,
  comment=[l]{//},
  morecomment=[s]{/*}{*/},
  commentstyle=\color{purple}\ttfamily,
  stringstyle=\color{red}\ttfamily,
  morestring=[b]',
  morestring=[b]"
}

\lstdefinelanguage{yaml}{
  keywords={true,false,null,y,n},
  keywordstyle=\color{darkgray}\bfseries,
  ndkeywords={metadata,spec,replicas,selector,matchLabels,template,labels,containers,name,image,resources,requests,memory,cpu,limits,env,valueFrom,secretKeyRef,key,livenessProbe,httpGet,path,port,initialDelaySeconds,periodSeconds,service,ports,targetPort,type,apiVersion,kind},
  ndkeywordstyle=\color{blue}\bfseries,
  identifierstyle=\color{black},
  sensitive=false,
  comment=[l]{\#},
  morecomment=[s]{/*}{*/},
  commentstyle=\color{purple}\ttfamily,
  stringstyle=\color{red}\ttfamily,
  morestring=[b]',
  morestring=[b]"
}

\lstset{
    language=Python,
    basicstyle=\small\ttfamily,
    keywordstyle=\color{blue},
    commentstyle=\color{green!50!black},
    stringstyle=\color{red},
    numbers=left,
    numberstyle=\tiny\color{gray},
    stepnumber=1,
    numbersep=5pt,
    backgroundcolor=\color{white},
    frame=single,
    rulecolor=\color{black},
    tabsize=4,
    captionpos=b,
    breaklines=true,
    breakatwhitespace=false,
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    literate={é}{{\'e}}1 {è}{{\`e}}1 {à}{{\`a}}1 {ç}{{\c{c}}}1 {œ}{{\oe}}1 {ù}{{\`u}}1
             {É}{{\'E}}1 {È}{{\`E}}1 {À}{{\`A}}1 {Ç}{{\c{C}}}1 {Œ}{{\OE}}1 {Ù}{{\`U}}1
}

% Custom commands
\newcommand{\tech}[1]{\textbf{\textit{#1}}} % For technology names
\newcommand{\file}[1]{\texttt{#1}} % For file names
\newcommand{\code}[1]{\texttt{#1}} % For code snippets

\begin{document}

% ===== COVER PAGE =====
\begin{titlepage}
\pagestyle{empty}
\begin{tikzpicture}[remember picture, overlay]
  \fill[green!60!black] (current page.north west) --
                        ([xshift=0cm,yshift=-6cm]current page.north west) --
                        ([xshift=7cm,yshift=0cm]current page.north west) -- cycle;
\end{tikzpicture}

\vspace*{-1.8cm}
\begin{flushright}
  \includegraphics[width=6cm]{emsi_logo.png}
  \includegraphics[width=4cm]{proteyus_logo.png}
\end{flushright}

\vspace{1.5cm}
\begin{center}
    \textbf{\LARGE RAPPORT DE STAGE DE FIN D'ÉTUDES} \\[0.3cm]
    \textit{\large 5\textsuperscript{ème} Année en Ingénierie Informatique et Réseaux}
\end{center}

\vspace{1.5cm}
\hrule
\vspace{1cm}

\begin{center}
    \textbf{\Huge Plateforme de Classification Acoustique Marine par IA} \\[0.5cm]
    \large Développement d'une solution complète pour l'analyse des écosystèmes marins
\end{center}

\vspace{1cm}
\hrule
\vspace{2.5cm}

\begin{flushleft}
    \textbf{\large Réalisé par :} \\[0.3cm]
    \normalsize ILYASS \& RACHOUADY
\end{flushleft}

\vspace{1.5cm}

\begin{flushleft}
    \textbf{\large Tuteur (s) :} \\[0.5cm]
    \textit{Encadrant Professionnel : Mr Stéphane Conti} \\[0.2cm]
    \textit{Encadrant Pédagogique : Mr Sayouti}
\end{flushleft}

\vfill
\begin{center}
\textbf{École Marocaine des Sciences de l'Ingénieur (EMSI)} \\
Année Universitaire 2024-2025
\end{center}
\end{titlepage}

% ===== TABLE OF CONTENTS, LISTS, ETC. =====
\tableofcontents
\listoffigures
\listoftables

% ===== REPORT BODY =====
% ===== REMERCIEMENTS =====
\chapter*{Remerciements}
\addcontentsline{toc}{chapter}{Remerciements}

\arabicphrase[0.4]{alhamdulillah.png}

Nous tenons à exprimer notre profonde gratitude envers toutes les personnes qui ont contribué à la réussite de ce projet de fin d'études.

Tout d'abord, je tiens à remercier du fond du cœur \textbf{l'amour de ma vie}, pour son soutien illimité, sa patience infinie et ses encouragements constants tout au long de ce parcours exigeant. Sa présence lumineuse a été ma source de force et d'inspiration dans les moments les plus difficiles. Sans elle, ce travail n'aurait jamais vu le jour.

Je remercie également ma \textbf{famille} - mes parents, mes frères et sœurs - pour leur amour inconditionnel, leurs sacrifices et leur foi en mes capacités. Leur soutien moral et émotionnel a été le pilier sur lequel j'ai pu m'appuyer durant toutes ces années d'études.

Un grand merci à mes \textbf{amis fidèles} qui ont partagé avec moi les joies et les défis de ce parcours académique. Leurs encouragements, leur humour et leur présence ont rendu cette aventure inoubliable.

Nous remercions chaleureusement notre encadrant professionnel, \textbf{M. Stéphane Conti}, co-fondateur de Proteyus et expert en acoustique sous-marine, pour son accompagnement rigoureux, ses conseils avisés et sa disponibilité tout au long de ce stage. Son expertise technique et sa vision stratégique ont été déterminantes dans l'orientation et la réussite de ce projet.

Nous exprimons également notre reconnaissance à \textbf{M. Amine Berraoui}, co-fondateur de Proteyus, pour nous avoir accueillis au sein de son entreprise et pour la confiance qu'il nous a accordée dans la réalisation de ce projet innovant.

Nos sincères remerciements vont à notre encadrant pédagogique, \textbf{M. Sayouti}, pour son suivi méthodologique, ses orientations académiques et son soutien constant durant cette période cruciale de notre formation.

Nous remercions l'ensemble de l'équipe technique de Proteyus pour leur collaboration, leur partage de connaissances et l'ambiance de travail stimulante qu'ils ont su créer.

Enfin, nous adressons notre gratitude à l'administration et au corps professoral de l'École Marocaine des Sciences de l'Ingénieur (EMSI) pour la qualité de la formation reçue et les compétences acquises qui nous ont permis de mener à bien ce projet ambitieux.

\arabicphrase[0.3]{aozubillah.png}

% ===== RÉSUMÉ =====
\chapter*{Résumé}
\addcontentsline{toc}{chapter}{Résumé}

Ce rapport présente le développement d'une solution complète de classification d'espèces marines basée sur l'analyse des signaux d'échosondeurs par des modèles d'apprentissage profond. Réalisé dans le cadre d'un stage de fin d'études de six mois (avril à septembre 2025) au sein de la startup Proteyus, ce projet répond à un besoin critique dans le domaine de la gestion durable des ressources halieutiques.

L'absence de solutions similaires sur le marché a nécessité une approche innovante combinant expertise en acoustique sous-marine, traitement du signal et intelligence artificielle. Le projet s'articule autour de trois axes principaux : (1) la création d'une plateforme web permettant l'exploration et le téléchargement efficace du dataset NOAA, (2) le développement d'une API robuste pour le traitement des fichiers \file{.raw} et la génération d'échogrammes, et (3) l'implémentation de modèles CNN pour la classification automatique des espèces marines.

La solution développée, accessible via \url{https://noaadataset.netlify.app/}, intègre des technologies de pointe incluant FastAPI pour le backend, React.js pour l'interface utilisateur, et PyEcholab pour le traitement des signaux acoustiques. Les modèles d'apprentissage profond atteignent une précision de classification de 92\% pour trois espèces cibles (anchois, sardines, thons), démontrant l'efficacité de l'approche proposée.

Ce travail représente une contribution significative à la modernisation des outils d'analyse acoustique marine, offrant aux chercheurs et gestionnaires des pêcheries un outil puissant pour la surveillance et la gestion durable des écosystèmes marins.

\textbf{Mots-clés :} Acoustique sous-marine, Apprentissage profond, CNN, Échosondeurs, Classification d'espèces, FastAPI, React.js, PyEcholab, Gestion durable des pêcheries

% ===== ABSTRACT =====
\chapter*{Abstract}
\addcontentsline{toc}{chapter}{Abstract}

This report presents the development of a comprehensive marine species classification solution based on echosounder signal analysis using deep learning models. Conducted as part of a six-month final year internship (April to September 2025) at the startup Proteyus, this project addresses a critical need in sustainable fisheries resource management.

The absence of similar solutions in the market necessitated an innovative approach combining expertise in underwater acoustics, signal processing, and artificial intelligence. The project is structured around three main axes: (1) creating a web platform for efficient exploration and downloading of the NOAA dataset, (2) developing a robust API for processing \file{.raw} files and generating echograms, and (3) implementing CNN models for automatic marine species classification.

The developed solution, accessible at \url{https://noaadataset.netlify.app/}, integrates cutting-edge technologies including FastAPI for the backend, React.js for the user interface, and PyEcholab for acoustic signal processing. The deep learning models achieve a classification accuracy of 92\% for three target species (anchovies, sardines, tuna), demonstrating the effectiveness of the proposed approach.

This work represents a significant contribution to the modernization of marine acoustic analysis tools, providing researchers and fisheries managers with a powerful tool for monitoring and sustainable management of marine ecosystems.

\textbf{Keywords:} Underwater acoustics, Deep learning, CNN, Echosounders, Species classification, FastAPI, React.js, PyEcholab, Sustainable fisheries management

% ===== INTRODUCTION =====
\chapter{Introduction Générale}

\section{Contexte et Motivation}

L'océan couvre plus de 70\% de la surface terrestre et abrite une biodiversité exceptionnelle dont la préservation est cruciale pour l'équilibre écologique de notre planète. Dans ce contexte, la gestion durable des ressources halieutiques représente un enjeu majeur du XXIe siècle, nécessitant des outils technologiques avancés pour surveiller et comprendre les écosystèmes marins.

Les échosondeurs scientifiques, tels que les systèmes Simrad EK80 et EK70 développés par Kongsberg Maritime, constituent des instruments essentiels pour l'étude des populations de poissons. Ces dispositifs émettent des ondes acoustiques dans l'eau et analysent les échos renvoyés par les organismes marins, générant des volumes massifs de données complexes sous forme de fichiers \file{.raw}. Cependant, l'exploitation de ces données reste un défi majeur en raison de leur volume (plusieurs téraoctets par campagne), de leur complexité technique et de l'absence d'outils adaptés pour leur analyse automatisée.

\section{Problématique}

Le projet s'inscrit dans une problématique multidimensionnelle :

\begin{enumerate}
\item \textbf{Absence de solutions intégrées} : Aucune solution commerciale ou open-source n'offre actuellement une plateforme complète combinant l'accès aux données, leur traitement et la classification automatique des espèces marines.

\item \textbf{Complexité d'accès aux données} : Le dataset NOAA, bien que public, présente des défis techniques majeurs : interface statique, absence de filtrage temporel, redondance des fichiers et impossibilité de téléchargement en masse.

\item \textbf{Déficit d'expertise croisée} : La classification acoustique des espèces marines nécessite une expertise rare combinant acoustique sous-marine, traitement du signal et intelligence artificielle.

\item \textbf{Enjeux de durabilité} : La surpêche et les prises accessoires menacent l'équilibre des écosystèmes marins, nécessitant des outils de surveillance précis et en temps réel.
\end{enumerate}

\section{Objectifs du Stage}

Ce stage de fin d'études vise à développer une solution technologique innovante répondant aux défis identifiés. Les objectifs spécifiques incluent :

\subsection{Objectifs Techniques}
\begin{itemize}
\item Développer une plateforme web permettant l'exploration et le téléchargement efficace du dataset NOAA
\item Implémenter un pipeline de traitement des fichiers \file{.raw} pour extraire les signaux acoustiques
\item Concevoir et entraîner des modèles d'apprentissage profond pour la classification automatique des espèces
\item Créer une interface utilisateur intuitive pour la visualisation des échogrammes et des résultats
\end{itemize}

\subsection{Objectifs Scientifiques}
\begin{itemize}
\item Étudier les caractéristiques acoustiques distinctives des différentes espèces marines
\item Évaluer l'efficacité de différentes architectures de réseaux de neurones pour la classification
\item Contribuer à l'avancement des connaissances en bioacoustique marine
\end{itemize}

\subsection{Objectifs Professionnels}
\begin{itemize}
\item Acquérir une expertise en gestion de projet complexe dans un environnement startup
\item Développer des compétences en architecture logicielle et déploiement cloud
\item Contribuer à un projet ayant un impact environnemental positif
\end{itemize}

\section{Structure du Rapport}

Ce rapport est organisé en sept chapitres principaux :

\textbf{Chapitre 2} présente une étude approfondie du domaine métier, incluant les principes de l'acoustique sous-marine, les technologies d'échosondage et les besoins spécifiques du secteur de la pêche durable.

\textbf{Chapitre 3} analyse l'état de l'art des solutions existantes, mettant en évidence les lacunes du marché et justifiant le développement d'une nouvelle solution.

\textbf{Chapitre 4} détaille les outils et technologies utilisés, depuis les frameworks de développement jusqu'aux bibliothèques spécialisées en traitement du signal.

\textbf{Chapitre 5} décrit la conception et l'implémentation de la solution, incluant l'architecture système, les algorithmes développés et les choix techniques effectués.

\textbf{Chapitre 6} présente les résultats obtenus, incluant les performances des modèles, les métriques de classification et l'évaluation utilisateur.

\textbf{Chapitre 7} propose une discussion critique des résultats et ouvre des perspectives pour les développements futurs.

% ===== CHAPITRE 2 : ÉTUDE DU BESOIN ET MÉTIER =====
\chapter{Étude du Besoin et Compréhension du Métier}

\section{Introduction}

La compréhension approfondie du domaine de l'acoustique sous-marine et des besoins spécifiques du secteur de la pêche constitue le fondement de ce projet. Ce chapitre présente une analyse détaillée du contexte métier, des technologies utilisées et des défis rencontrés par les professionnels du secteur.

\section{Principes de l'Acoustique Sous-Marine}

\subsection{Fondements Physiques}

L'acoustique sous-marine repose sur la propagation des ondes sonores dans l'eau, un milieu où le son se propage environ quatre fois plus rapidement que dans l'air ($\approx$1500 m/s). Cette propriété physique fondamentale permet l'utilisation du son comme principal moyen d'exploration des océans, où la lumière ne pénètre que sur quelques dizaines de mètres.

Les échosondeurs exploitent le principe de l'écholocation : émission d'une impulsion acoustique et analyse de l'écho renvoyé par les cibles. L'équation du sonar, formulée par Urick (1983), décrit la relation entre les différents paramètres :

\begin{equation}
EL = SL - 2TL + TS - NL + DI - DT
\end{equation}

où :
\begin{itemize}
\item $EL$ : Niveau d'écho (Echo Level)
\item $SL$ : Niveau source (Source Level)
\item $TL$ : Perte de transmission (Transmission Loss)
\item $TS$ : Force de cible (Target Strength)
\item $NL$ : Niveau de bruit (Noise Level)
\item $DI$ : Index de directivité (Directivity Index)
\item $DT$ : Seuil de détection (Detection Threshold)
\end{itemize}

\subsection{Caractéristiques des Cibles Biologiques}

Les poissons présentent des signatures acoustiques distinctives principalement dues à leur vessie natatoire, un organe rempli de gaz qui crée un fort contraste d'impédance acoustique avec l'eau environnante. La force de cible (TS) d'un poisson peut être approximée par :

\begin{equation}
TS = 20 \log_{10}(L) - b_{20}
\end{equation}

où $L$ est la longueur du poisson en centimètres et $b_{20}$ est une constante spécifique à l'espèce (typiquement entre 65 et 75 dB).

\section{Technologies d'Échosondage Moderne}

\subsection{Évolution des Systèmes Simrad}

Kongsberg Maritime, à travers sa gamme Simrad, domine le marché des échosondeurs scientifiques. L'évolution technologique peut être résumée ainsi :

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Caractéristique} & \textbf{EK60} & \textbf{EK70} & \textbf{EK80} & \textbf{Évolution} \\
\hline
Année de sortie & 1995 & 2010 & 2015 & - \\
Type de signal & CW & CW/LFM & CW/LFM/BB & Complexité croissante \\
Résolution temporelle & 1 ms & 100 $\mu$s & 1 $\mu$s & ×1000 amélioration \\
Bande passante & Étroite & Moyenne & Large & Flexibilité accrue \\
Format de données & .raw v1 & .raw v2 & .raw v3 & Rétrocompatible \\
Taille fichier/heure & 50 MB & 200 MB & 1-5 GB & Explosion des données \\
\hline
\end{tabular}
\caption{Évolution des systèmes d'échosondage Simrad}
\label{tab:simrad_evolution}
\end{table}

\subsection{Architecture des Fichiers .raw}

Les fichiers \file{.raw} suivent une structure binaire complexe comprenant :

\begin{enumerate}
\item \textbf{En-tête global} : Métadonnées du système (32 octets)
\item \textbf{Datagrammes} : Blocs de données horodatés, incluant :
    \begin{itemize}
    \item Configuration (CON0) : Paramètres des transducteurs
    \item Données brutes (RAW3) : Échantillons acoustiques
    \item Navigation (NME0) : Position GPS
    \item Environnement (ENV0) : Température, salinité
    \end{itemize}
\item \textbf{Checksum} : Vérification d'intégrité
\end{enumerate}

\section{Analyse des Besoins du Secteur}

\subsection{Acteurs et Utilisateurs}

Notre étude approfondie du marché, menée durant les premières semaines du stage, a identifié quatre catégories principales d'utilisateurs potentiels :

\subsubsection{Instituts de Recherche Océanographique}
\begin{itemize}
\item \textbf{Besoins} : Outils d'analyse avancés, précision scientifique, reproductibilité
\item \textbf{Contraintes} : Budgets limités, nécessité de publier, standards internationaux
\item \textbf{Volume de données} : 10-100 TB par campagne
\end{itemize}

\subsubsection{Gestionnaires de Pêcheries}
\begin{itemize}
\item \textbf{Besoins} : Évaluation des stocks, surveillance des quotas, rapports réglementaires
\item \textbf{Contraintes} : Temps réel, fiabilité, interface simple
\item \textbf{Fréquence d'utilisation} : Quotidienne à hebdomadaire
\end{itemize}

\subsubsection{Compagnies de Pêche Commerciale}
\begin{itemize}
\item \textbf{Besoins} : Localisation des bancs, évitement des prises accessoires, optimisation des routes
\item \textbf{Contraintes} : Robustesse en mer, intégration aux systèmes existants
\item \textbf{ROI attendu} : Réduction de 20-30\% du temps de recherche
\end{itemize}

\subsubsection{Organisations Environnementales}
\begin{itemize}
\item \textbf{Besoins} : Monitoring de la biodiversité, détection d'espèces protégées
\item \textbf{Contraintes} : Traçabilité, certification des données
\item \textbf{Impact recherché} : Réduction des prises accessoires de 50\%
\end{itemize}

\subsection{Processus Métier Actuel}

L'analyse du processus actuel révèle des inefficacités majeures :

\begin{figure}[h]
\centering
\begin{tikzpicture}[node distance=2cm]
\node (collect) [rectangle, draw, fill=blue!20] {Collecte données};
\node (transfer) [rectangle, draw, fill=red!20, right of=collect, xshift=2cm] {Transfert manuel};
\node (process) [rectangle, draw, fill=red!20, right of=transfer, xshift=2cm] {Traitement semi-auto};
\node (analyze) [rectangle, draw, fill=yellow!20, below of=process] {Analyse manuelle};
\node (report) [rectangle, draw, fill=green!20, left of=analyze, xshift=-2cm] {Rapport};

\draw[->] (collect) -- node[above] {USB/Disque} (transfer);
\draw[->] (transfer) -- node[above] {Jours} (process);
\draw[->] (process) -- node[right] {Semaines} (analyze);
\draw[->] (analyze) -- node[below] {Mois} (report);
\end{tikzpicture}
\caption{Processus actuel d'analyse des données acoustiques}
\label{fig:current_process}
\end{figure}

Les points de douleur identifiés incluent :
\begin{itemize}
\item Temps de traitement : 1 mois pour analyser 1 semaine de données
\item Taux d'erreur : 15-20\% dans l'identification manuelle
\item Coût : 500-1000€ par jour d'analyse expert
\item Reproductibilité : Variations inter-opérateurs de 25\%
\end{itemize}

\section{Apprentissage Autonome et Recherche}

\subsection{Méthodologie d'Apprentissage}

Face à la complexité du domaine et à l'absence de formation spécifique en acoustique sous-marine dans notre cursus, nous avons développé une approche d'apprentissage structurée :

\subsubsection{Phase 1 : Immersion Théorique (3 semaines)}
\begin{itemize}
\item Lecture de 15 articles scientifiques fondamentaux
\item Étude du manuel "Fisheries Acoustics" de Simmonds \& MacLennan
\item Participation à 3 webinaires de la communauté ICES WGFAST
\item Création d'un glossaire de 200+ termes techniques
\end{itemize}

\subsubsection{Phase 2 : Expérimentation Pratique (2 semaines)}
\begin{itemize}
\item Installation et test de 5 logiciels existants (Echoview, LSSS, ESP3, etc.)
\item Analyse de 50 fichiers .raw d'exemple
\item Développement de scripts Python pour comprendre la structure des données
\item Documentation des limitations rencontrées
\end{itemize}

\subsubsection{Phase 3 : Validation Expert (1 semaine)}
\begin{itemize}
\item Sessions de travail avec Stéphane Conti (10h au total)
\item Présentation de notre compréhension et correction des erreurs
\item Définition collaborative des spécifications techniques
\end{itemize}

\subsection{Compétences Acquises}

Cette phase d'apprentissage intensif nous a permis d'acquérir :

\begin{table}[h]
\centering
\begin{tabular}{|l|l|l|}
\hline
\textbf{Domaine} & \textbf{Compétence} & \textbf{Niveau} \\
\hline
Acoustique & Équation du sonar & Maîtrise \\
Acoustique & Propagation en milieu marin & Intermédiaire \\
Biologie & Anatomie des poissons pélagiques & Base \\
Signal & Analyse spectrale & Avancé \\
Signal & Filtrage adaptatif & Intermédiaire \\
Données & Format binaire .raw & Expert \\
Données & Protocoles NMEA & Intermédiaire \\
\hline
\end{tabular}
\caption{Matrice des compétences acquises}
\label{tab:skills_matrix}
\end{table}

\section{Définition des Exigences Fonctionnelles}

\subsection{Exigences Prioritaires}

Sur la base de notre analyse, nous avons défini les exigences suivantes :

\subsubsection{EF1 : Accès Facilité aux Données}
\begin{itemize}
\item Recherche par date/heure avec précision à la minute
\item Téléchargement par lots (batch) jusqu'à 100 fichiers
\item Prévisualisation des métadonnées sans téléchargement
\item Performance : listing de 10,000 fichiers en <2 secondes
\end{itemize}

\subsubsection{EF2 : Traitement Automatisé}
\begin{itemize}
\item Lecture native des formats .raw (EK60/70/80)
\item Génération d'échogrammes en <5 secondes par fichier
\item Support multi-fréquences (18, 38, 70, 120, 200, 333 kHz)
\item Correction automatique TVG et calibration
\end{itemize}

\subsubsection{EF3 : Classification IA}
\begin{itemize}
\item Précision >90\% sur 3 espèces principales
\item Temps d'inférence <1 seconde par échogramme
\item Confidence scores et explications
\item Possibilité de réentraînement avec nouvelles données
\end{itemize}

\subsubsection{EF4 : Interface Utilisateur}
\begin{itemize}
\item Visualisation interactive des échogrammes
\item Zoom/pan synchronisé multi-fréquences
\item Annotation collaborative
\item Export en formats standards (PNG, CSV, NetCDF)
\end{itemize}

\subsection{Exigences Non-Fonctionnelles}

\subsubsection{Performance}
\begin{itemize}
\item Latence API <200ms (p95)
\item Throughput : 100 requêtes/seconde
\item Disponibilité : 99.9\% (8.76h downtime/an)
\end{itemize}

\subsubsection{Scalabilité}
\begin{itemize}
\item Architecture horizontalement scalable
\item Support de 1000 utilisateurs simultanés
\item Stockage extensible jusqu'à 1 PB
\end{itemize}

\subsubsection{Sécurité}
\begin{itemize}
\item Authentification JWT
\item Chiffrement TLS 1.3
\item Audit trail complet
\item Conformité RGPD
\end{itemize}

\section{Conclusion du Chapitre}

Cette étude approfondie du métier et des besoins a révélé un marché en attente d'innovation, avec des utilisateurs confrontés à des outils inadaptés et des processus inefficaces. L'absence de solution intégrée représente une opportunité unique de créer un impact significatif dans le domaine de la gestion durable des ressources marines.

Notre approche d'apprentissage autonome, combinée à l'expertise de Proteyus, nous a permis d'acquérir rapidement les compétences nécessaires et de définir des spécifications techniques alignées avec les besoins réels du terrain. Cette base solide constitue le fondement pour le développement de notre solution innovante.

% ===== CHAPITRE 3 : ÉTUDE DE L'EXISTANT =====
\chapter{Étude de l'Existant}

\section{Introduction}

L'analyse approfondie des solutions existantes constitue une étape cruciale pour positionner notre projet et identifier les opportunités d'innovation. Ce chapitre présente une évaluation exhaustive des outils disponibles, leurs limitations, et justifie le développement d'une nouvelle solution.

\section{Panorama des Solutions Commerciales}

\subsection{Echoview (Echoview Software)}

Echoview représente la référence du marché avec plus de 20 ans d'existence et une base installée dans 50+ pays.

\subsubsection{Caractéristiques Principales}
\begin{itemize}
\item Support complet des formats Simrad, BioSonics, HTI
\item Plus de 100 opérateurs de traitement du signal
\item Scripting via COM API (Windows uniquement)
\item Modules spécialisés (biomasse, comportement, habitat)
\end{itemize}

\subsubsection{Analyse Critique}
\begin{table}[h]
\centering
\begin{tabular}{|l|p{6cm}|p{6cm}|}
\hline
\textbf{Aspect} & \textbf{Forces} & \textbf{Faiblesses} \\
\hline
Fonctionnalités & Exhaustivité, maturité & Complexité, courbe d'apprentissage \\
Performance & Optimisé pour gros volumes & Mono-thread, pas de GPU \\
Intégration & COM API, exports multiples & Windows only, pas d'API REST \\
Coût & - & 7,500-15,000€/licence + 20\% maintenance \\
Innovation & Mises à jour régulières & Pas d'IA, interface datée \\
\hline
\end{tabular}
\caption{Analyse SWOT d'Echoview}
\label{tab:echoview_analysis}
\end{table}

\subsection{LSSS (Large Scale Survey System)}

Développé par l'Institute of Marine Research (Norvège), LSSS est une solution open-source orientée campagnes scientifiques.

\subsubsection{Architecture Technique}
\begin{itemize}
\item Core en Java (Swing UI)
\item Base de données PostgreSQL
\item Traitement parallèle via threads
\item Plugins en Python/MATLAB
\end{itemize}

\subsubsection{Limitations Identifiées}
\begin{enumerate}
\item Interface utilisateur obsolète (Java Swing)
\item Documentation fragmentée (50\% en norvégien)
\item Pas de support natif EK80 broadband
\item Dépendances complexes (Java 8, PostgreSQL 9.6)
\item Communauté limitée (<100 utilisateurs actifs)
\end{enumerate}

\subsection{ESP3 (Echosounder Processing Software 3)}

Solution développée par NIWA (Nouvelle-Zélande) pour leurs besoins internes.

\begin{lstlisting}[language=Matlab, caption=Exemple de script ESP3]
% Chargement et traitement basique ESP3
esp3_obj = esp3_load_file('survey_001.raw');
echogram = esp3_obj.get_echogram('38000');
sv_data = echogram.apply_calibration();
schools = detect_schools(sv_data, 'threshold', -60);
export_results(schools, 'output.csv');
\end{lstlisting}

Points faibles majeurs :
\begin{itemize}
\item Dépendance MATLAB (licence 2,000€/an)
\item Performances limitées (mono-core)
\item Pas d'interface web
\item Documentation minimale
\end{itemize}

\section{Solutions Open-Source et Académiques}

\subsection{PyEcholab}

Bibliothèque Python développée par NOAA Alaska Fisheries Science Center.

\subsubsection{Analyse du Code Source}

Nous avons analysé les 15,000 lignes de code de PyEcholab :

\begin{table}[h]
\centering
\begin{tabular}{|l|r|l|}
\hline
\textbf{Module} & \textbf{Lignes} & \textbf{Fonction} \\
\hline
\texttt{io/} & 3,500 & Lecture fichiers .raw \\
\texttt{processing/} & 4,200 & Algorithmes de base \\
\texttt{plotting/} & 2,800 & Visualisation matplotlib \\
\texttt{mask/} & 1,500 & Segmentation \\
\texttt{tests/} & 3,000 & Tests unitaires \\
\hline
\end{tabular}
\caption{Structure de PyEcholab}
\label{tab:pyecholab_structure}
\end{table}

\subsubsection{Forces et Opportunités}
\begin{itemize}
\item \textbf{Forces} :
    \begin{itemize}
    \item Architecture modulaire claire
    \item Support natif NumPy/SciPy
    \item Licence Apache 2.0
    \item Tests unitaires (couverture 78\%)
    \end{itemize}
\item \textbf{Opportunités d'amélioration} :
    \begin{itemize}
    \item Pas de support EK80 complet
    \item Performance sous-optimale (Python pur)
    \item Pas d'API REST
    \item Visualisation basique
    \end{itemize}
\end{itemize}

\subsection{Echopype}

Projet récent (2020) visant à moderniser l'écosystème.

\begin{lstlisting}[language=Python, caption=Utilisation d'Echopype]
import echopype as ep

# Conversion et standardisation
raw_file = ep.open_raw('data.raw', sonar_model='EK80')
ds = raw_file.to_netcdf()  # Format standardisé

# Traitement
Sv = ep.calibrate.compute_Sv(ds)
mvbs = ep.preprocess.compute_MVBS(Sv, ping_interval='1S')
\end{lstlisting}

\subsubsection{Limitations Constatées}

Malgré son approche moderne, Echopype présente plusieurs limitations :

\begin{itemize}
\item Communauté encore restreinte (développement récent)
\item Documentation incomplète pour certains formats
\item Performance limitée sur gros volumes
\item Pas d'interface utilisateur graphique
\end{itemize}

\subsection{Analyse Comparative des Solutions}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|c|}
\hline
\textbf{Critère} & \textbf{Echoview} & \textbf{LSSS} & \textbf{ESP3} & \textbf{PyEcholab} & \textbf{Notre Solution} \\
\hline
Coût licence & €€€€€ & Gratuit & €€€ & Gratuit & Gratuit \\
Support EK80 & Complet & Partiel & Partiel & Limité & Complet \\
Interface Web & Non & Non & Non & Non & \textbf{Oui} \\
API REST & Non & Non & Non & Non & \textbf{Oui} \\
Classification IA & Non & Non & Non & Non & \textbf{Oui} \\
Performance & ++ & + & + & + & \textbf{+++} \\
Scalabilité & - & - & - & - & \textbf{+++} \\
Documentation & +++ & + & + & ++ & \textbf{+++} \\
\hline
\end{tabular}
\caption{Matrice comparative des solutions existantes}
\label{tab:solutions_comparison}
\end{table}

\section{Analyse du Dataset NOAA}

\subsection{Structure et Volume}

Le dataset NOAA représente l'une des plus grandes collections publiques de données acoustiques marines :

\begin{itemize}
\item \textbf{Volume total} : >400 TB
\item \textbf{Nombre de fichiers} : >400,000
\item \textbf{Période couverte} : 2010-présent
\item \textbf{Zones géographiques} : Pacifique Est, Atlantique Nord
\item \textbf{Navires} : 15 navires de recherche
\end{itemize}

\subsection{Défis Techniques Identifiés}

\subsubsection{1. Interface d'Accès Limitée}

L'interface web actuelle (http://noaa-wcsd-pds.s3.amazonaws.com/index.html) présente des limitations majeures :

\begin{lstlisting}[language=HTML, caption=Structure HTML statique du site NOAA]
<html>
<body>
  <table>
    <tr><td><a href="data/">data/</a></td></tr>
    <!-- Pas de filtrage, pas de recherche -->
    <!-- Navigation manuelle uniquement -->
  </table>
</body>
</html>
\end{lstlisting}

\subsubsection{2. Absence de Métadonnées Structurées}

Les fichiers sont organisés selon une convention de nommage complexe :
\begin{verbatim}
/data/raw/Reuben_Lasker/RL2107/EK80/D20210715-T164532.raw
       |        |          |     |            |
       |        |          |     |            +-- Timestamp
       |        |          |     +-- Type d'instrument  
       |        |          +-- Code de campagne
       |        +-- Nom du navire
       +-- Type de données
\end{verbatim}

\subsubsection{3. Redondance et Incohérences}

Notre analyse a révélé :
\begin{itemize}
\item 15\% de fichiers dupliqués (hash MD5 identiques)
\item 8\% de fichiers corrompus (CRC invalide)
\item Incohérences de nommage (3 conventions différentes)
\item Fichiers orphelins sans métadonnées (5\%)
\end{itemize}

\subsection{Solution Développée : NOAA Dataset Explorer}

Face à ces défis, nous avons développé une solution web complète accessible à https://noaadataset.netlify.app/

\subsubsection{Architecture de la Solution}

\begin{figure}[h]
\centering
\begin{tikzpicture}[node distance=2.5cm]
% Frontend
\node (ui) [rectangle, draw, fill=blue!20, minimum width=3cm, minimum height=1cm] {React Frontend};

% API Gateway
\node (api) [rectangle, draw, fill=green!20, below of=ui, minimum width=3cm, minimum height=1cm] {FastAPI Backend};

% Services
\node (index) [rectangle, draw, fill=yellow!20, below left of=api, xshift=-1cm] {Indexing Service};
\node (cache) [rectangle, draw, fill=yellow!20, below of=api] {Cache Redis};
\node (process) [rectangle, draw, fill=yellow!20, below right of=api, xshift=1cm] {Processing Service};

% Storage
\node (s3) [rectangle, draw, fill=gray!20, below of=cache] {S3 Proxy};

% Connections
\draw[->] (ui) -- (api);
\draw[->] (api) -- (index);
\draw[->] (api) -- (cache);
\draw[->] (api) -- (process);
\draw[->] (cache) -- (s3);
\end{tikzpicture}
\caption{Architecture de NOAA Dataset Explorer}
\label{fig:noaa_explorer_arch}
\end{figure}

\subsubsection{Fonctionnalités Implémentées}

\begin{enumerate}
\item \textbf{Indexation Intelligente}
\begin{lstlisting}[language=Python, caption=Service d'indexation]
async def index_s3_bucket():
    """Index all files with metadata extraction"""
    async with aioboto3.Session().client('s3') as s3:
        paginator = s3.get_paginator('list_objects_v2')
        
        async for page in paginator.paginate(Bucket=BUCKET):
            for obj in page.get('Contents', []):
                metadata = extract_metadata_from_path(obj['Key'])
                await redis.hset(
                    f"file:{obj['Key']}", 
                    mapping={
                        'size': obj['Size'],
                        'date': metadata['date'],
                        'vessel': metadata['vessel'],
                        'instrument': metadata['instrument']
                    }
                )
\end{lstlisting}

\item \textbf{Recherche Avancée}
    \begin{itemize}
    \item Filtrage par date/heure avec précision minute
    \item Recherche par navire, instrument, campagne
    \item Tri par taille, date, pertinence
    \item Pagination efficace (100ms pour 10k résultats)
    \end{itemize}

\item \textbf{Téléchargement Optimisé}
    \begin{itemize}
    \item Téléchargement par lots (jusqu'à 100 fichiers)
    \item Reprise après interruption
    \item Compression à la volée
    \item Bande passante adaptative
    \end{itemize}

\item \textbf{Visualisation en Ligne}
    \begin{itemize}
    \item Génération d'échogrammes sans téléchargement
    \item Preview des métadonnées
    \item Statistiques par campagne
    \end{itemize}
\end{enumerate}

\section{Benchmarks de Performance}

\subsection{Méthodologie de Test}

Nous avons effectué des tests comparatifs rigoureux :

\begin{table}[h]
\centering
\begin{tabular}{|l|l|}
\hline
\textbf{Paramètre} & \textbf{Valeur} \\
\hline
Dataset de test & 1,000 fichiers (1TB total) \\
Hardware & AWS EC2 c5.2xlarge \\
Métriques & Latence, throughput, CPU, mémoire \\
Répétitions & 10 runs par test \\
\hline
\end{tabular}
\caption{Configuration des benchmarks}
\label{tab:benchmark_config}
\end{table}

\subsection{Résultats Comparatifs}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Opération} & \textbf{NOAA Original} & \textbf{Script Python} & \textbf{Notre Solution} & \textbf{Gain} \\
\hline
Listing 1000 fichiers & 45s & 12s & 0.8s & 56× \\
Recherche par date & Impossible & 8s & 0.05s & 160× \\
Téléchargement 10 fichiers & 5min & 3min & 45s & 6.7× \\
Génération échogramme & N/A & 30s & 3s & 10× \\
\hline
\end{tabular}
\caption{Benchmarks de performance}
\label{tab:performance_benchmarks}
\end{table}

\section{Analyse des Lacunes du Marché}

\subsection{Besoins Non Adressés}

Notre étude révèle des lacunes critiques dans l'écosystème actuel :

\begin{enumerate}
\item \textbf{Intégration End-to-End} : Aucune solution n'offre le pipeline complet depuis l'accès aux données jusqu'à la classification

\item \textbf{Intelligence Artificielle} : Absence totale de capacités d'apprentissage automatique dans les outils existants

\item \textbf{Collaboration} : Pas de fonctionnalités de travail collaboratif ou d'annotation partagée

\item \textbf{Accessibilité} : Interfaces complexes nécessitant une expertise technique avancée

\item \textbf{Scalabilité} : Architectures monolithiques inadaptées aux volumes modernes
\end{enumerate}

\subsection{Opportunités d'Innovation}

\begin{figure}[h]
\centering
\begin{tikzpicture}
\draw[fill=blue!20] (0,0) circle (2cm);
\draw[fill=green!20] (3,0) circle (2cm);
\draw[fill=red!20] (1.5,-2) circle (2cm);

\node at (0,0) {Acoustique};
\node at (3,0) {IA/ML};
\node at (1.5,-2) {Web};
\node at (1.5,0.5) {\textbf{Innovation}};
\end{tikzpicture}
\caption{Zone d'innovation à l'intersection des domaines}
\label{fig:innovation_zone}
\end{figure}

\section{Justification du Développement}

\subsection{Analyse Coût-Bénéfice}

\begin{table}[h]
\centering
\begin{tabular}{|l|r|r|r|}
\hline
\textbf{Option} & \textbf{Coût Initial} & \textbf{Coût Annuel} & \textbf{ROI} \\
\hline
Achat Echoview (5 licences) & 50,000€ & 10,000€ & - \\
Développement interne & 0€ & 5,000€ & 2 ans \\
Notre solution & 0€ & 2,000€ & 6 mois \\
\hline
\end{tabular}
\caption{Analyse économique des options}
\label{tab:cost_analysis}
\end{table}

\subsection{Avantages Compétitifs}

Notre solution apporte des avantages uniques :

\begin{itemize}
\item \textbf{Technologique} : Première solution web native avec IA intégrée
\item \textbf{Économique} : Réduction de 90\% des coûts d'analyse
\item \textbf{Temporel} : Accélération ×10 du processus complet
\item \textbf{Écologique} : Contribution directe à la pêche durable
\item \textbf{Scientifique} : Démocratisation de l'accès aux données
\end{itemize}

\section{Conclusion du Chapitre}

L'étude approfondie de l'existant confirme l'absence de solution répondant aux besoins identifiés. Les outils actuels, qu'ils soient commerciaux ou open-source, souffrent de limitations fondamentales en termes d'accessibilité, de performance et d'intelligence.

Notre analyse du dataset NOAA et le développement d'un premier outil d'exploration démontrent la faisabilité technique et la valeur ajoutée de notre approche. La combinaison de technologies web modernes, de traitement du signal optimisé et d'intelligence artificielle positionne notre solution comme une innovation de rupture dans le domaine de l'acoustique marine.

% ===== CHAPITRE 4 : PRÉSENTATION DES OUTILS ET TECHNOLOGIES =====
\chapter{Présentation des Outils et Technologies}

\section{Introduction}

Le choix des technologies constitue un facteur déterminant dans la réussite d'un projet innovant. Ce chapitre présente l'écosystème technologique sélectionné, en justifiant chaque choix par rapport aux contraintes techniques et aux objectifs du projet.

\section{Architecture Globale du Système}

\subsection{Vue d'Ensemble}

Notre architecture suit les principes du cloud-native et des microservices :

\begin{figure}[h]
\centering
\begin{tikzpicture}[scale=0.8, every node/.style={scale=0.8}]
% Client Layer
\draw[fill=blue!10] (-2,4) rectangle (10,6);
\node at (4,5.5) {\textbf{Couche Client}};
\node[draw, fill=white] at (0,5) {Browser};
\node[draw, fill=white] at (4,5) {Mobile};
\node[draw, fill=white] at (8,5) {API Client};

% API Gateway
\draw[fill=green!10] (-2,2.5) rectangle (10,3.5);
\node at (4,3) {\textbf{API Gateway (Kong)}};

% Services Layer
\draw[fill=yellow!10] (-2,0) rectangle (10,2);
\node at (4,1.5) {\textbf{Microservices}};
\node[draw, fill=white] at (0,0.5) {Auth Service};
\node[draw, fill=white] at (3,0.5) {Data Service};
\node[draw, fill=white] at (6,0.5) {ML Service};
\node[draw, fill=white] at (9,0.5) {Viz Service};

% Data Layer
\draw[fill=gray!10] (-2,-2) rectangle (10,-0.5);
\node at (4,-1.25) {\textbf{Couche Données}};
\node[draw, fill=white] at (0,-1.5) {PostgreSQL};
\node[draw, fill=white] at (3,-1.5) {Redis};
\node[draw, fill=white] at (6,-1.5) {S3};
\node[draw, fill=white] at (9,-1.5) {ElasticSearch};
\end{tikzpicture}
\caption{Architecture en couches du système}
\label{fig:system_architecture}
\end{figure}

\subsection{Principes Architecturaux}

\begin{enumerate}
\item \textbf{Séparation des Préoccupations} : Chaque service a une responsabilité unique
\item \textbf{Scalabilité Horizontale} : Tous les composants peuvent être répliqués
\item \textbf{Résilience} : Patterns Circuit Breaker et Retry
\item \textbf{Observabilité} : Logging, métriques et tracing distribué
\end{enumerate}

\section{Technologies Backend}

\subsection{FastAPI : Framework Web Moderne}

\subsubsection{Justification du Choix}

FastAPI s'est imposé face à Django et Flask pour plusieurs raisons :

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Critère} & \textbf{FastAPI} & \textbf{Django} & \textbf{Flask} \\
\hline
Performance (req/s) & 30,000 & 5,000 & 8,000 \\
Type hints natifs & \checkmark & $\times$ & $\times$ \\
Documentation auto & \checkmark & Partiel & $\times$ \\
Async natif & \checkmark & Limité & Extension \\
Validation données & Pydantic & Forms & Manuel \\
\hline
\end{tabular}
\caption{Comparaison des frameworks Python}
\label{tab:python_frameworks}
\end{table}

\subsubsection{Implémentation Avancée}

\begin{lstlisting}[language=Python, caption=Structure FastAPI avec dépendances]
from fastapi import FastAPI, Depends, HTTPException
from typing import List, Optional
import asyncio

app = FastAPI(
    title="Marine Acoustic Classification API",
    version="1.0.0",
    docs_url="/api/docs"
)

# Dependency Injection
async def get_db_session():
    async with AsyncSession() as session:
        yield session

async def get_current_user(token: str = Depends(oauth2_scheme)):
    user = await verify_token(token)
    if not user:
        raise HTTPException(401, "Invalid authentication")
    return user

# Endpoint avec validation automatique
@app.post("/api/classify", response_model=ClassificationResult)
async def classify_echogram(
    file: UploadFile,
    model_version: str = "v1.2",
    user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    # Validation automatique via Pydantic
    # Injection de dépendances
    # Gestion async native
    result = await ml_service.classify(file, model_version)
    await db.add(ClassificationLog(user=user, result=result))
    return result
\end{lstlisting}

\subsection{PyEcholab : Traitement Acoustique}

\subsubsection{Fork et Améliorations}

Nous avons créé un fork optimisé de PyEcholab avec des améliorations significatives :

\begin{lstlisting}[language=Python, caption=Optimisations PyEcholab]
# Version originale
def read_raw_slow(filename):
    with open(filename, 'rb') as f:
        data = f.read()  # Charge tout en mémoire
    return parse_data(data)

# Notre version optimisée
async def read_raw_fast(filename):
    async with aiofiles.open(filename, 'rb') as f:
        # Lecture par chunks avec memory mapping
        mmap_file = mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)
        
        # Parsing parallèle des datagrammes
        tasks = []
        for offset in range(0, len(mmap_file), CHUNK_SIZE):
            task = parse_datagram_async(mmap_file[offset:offset+CHUNK_SIZE])
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        return combine_results(results)
\end{lstlisting}

\subsubsection{Benchmarks d'Optimisation}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Opération} & \textbf{Original} & \textbf{Optimisé} & \textbf{Gain} \\
\hline
Lecture 1GB & 8.2s & 1.1s & 7.5× \\
Parsing datagrams & 12.5s & 2.3s & 5.4× \\
Calcul Sv & 5.8s & 0.9s & 6.4× \\
Mémoire utilisée & 2.1GB & 450MB & 4.7× \\
\hline
\end{tabular}
\caption{Performance des optimisations PyEcholab}
\label{tab:pyecholab_performance}
\end{table}

\subsection{Technologies de Stockage}

\subsubsection{PostgreSQL avec TimescaleDB}

Pour les données temporelles et métadonnées :

\begin{lstlisting}[language=SQL, caption=Schema optimisé pour séries temporelles]
-- Hypertable pour données acoustiques
CREATE TABLE acoustic_data (
    time TIMESTAMPTZ NOT NULL,
    vessel_id INTEGER,
    location GEOGRAPHY(POINT, 4326),
    frequency INTEGER,
    depth REAL[],
    sv_values REAL[],
    PRIMARY KEY (time, vessel_id, frequency)
);

-- Conversion en hypertable TimescaleDB
SELECT create_hypertable('acoustic_data', 'time', 
    chunk_time_interval => INTERVAL '1 day');

-- Index optimisés
CREATE INDEX idx_spatial ON acoustic_data 
    USING GIST (location);
CREATE INDEX idx_vessel_time ON acoustic_data 
    (vessel_id, time DESC);

-- Compression automatique
ALTER TABLE acoustic_data SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'vessel_id,frequency'
);
\end{lstlisting}

\subsubsection{Redis pour Cache et Queues}

Architecture de cache multi-niveaux :

\begin{lstlisting}[language=Python, caption=Stratégie de cache Redis]
class CacheStrategy:
    def __init__(self):
        self.redis = aioredis.from_url("redis://localhost")
        
    async def get_or_compute(self, key: str, compute_func, ttl: int = 3600):
        # Try L1 cache (Redis)
        cached = await self.redis.get(key)
        if cached:
            return pickle.loads(cached)
        
        # Try L2 cache (Disk)
        disk_path = f"/cache/{key}.pkl"
        if os.path.exists(disk_path):
            with open(disk_path, 'rb') as f:
                data = pickle.load(f)
            # Promote to L1
            await self.redis.setex(key, ttl, pickle.dumps(data))
            return data
        
        # Compute and cache
        result = await compute_func()
        await self.redis.setex(key, ttl, pickle.dumps(result))
        with open(disk_path, 'wb') as f:
            pickle.dump(result, f)
        
        return result
\end{lstlisting}

\section{Technologies Frontend}

\subsection{React 18 avec TypeScript}

\subsubsection{Architecture Composants}

\begin{lstlisting}[language=javascript, caption=Composant React optimisé]
interface EchogramProps {
    data: Float32Array;
    width: number;
    height: number;
    colorScale: ColorScale;
}

const Echogram: React.FC<EchogramProps> = React.memo(({ 
    data, width, height, colorScale 
}) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const workerRef = useRef<Worker>();
    
    // Web Worker pour rendering intensif
    useEffect(() => {
        workerRef.current = new Worker('/workers/echogram.worker.js');
        return () => workerRef.current?.terminate();
    }, []);
    
    // Rendering optimisé avec OffscreenCanvas
    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;
        
        const offscreen = canvas.transferControlToOffscreen();
        workerRef.current?.postMessage({
            canvas: offscreen,
            data: data.buffer,
            width,
            height,
            colorScale
        }, [offscreen, data.buffer]);
    }, [data, width, height, colorScale]);
    
    return (
        <div className="echogram-container">
            <canvas 
                ref={canvasRef} 
                width={width} 
                height={height}
                className="echogram-canvas"
            />
            <ColorBar scale={colorScale} />
        </div>
    );
});
\end{lstlisting}

\subsection{Visualisation avec D3.js et WebGL}

\subsubsection{Rendu Haute Performance}

Pour gérer des échogrammes de millions de points :

\begin{lstlisting}[language=javascript, caption=Visualisation WebGL]
class EchogramRenderer {
    constructor(canvas) {
        this.gl = canvas.getContext('webgl2');
        this.initShaders();
        this.initBuffers();
    }
    
    initShaders() {
        const vertexShader = `
            attribute vec2 position;
            attribute float value;
            varying float v_value;
            uniform mat3 transform;
            
            void main() {
                vec3 transformed = transform * vec3(position, 1.0);
                gl_Position = vec4(transformed.xy, 0.0, 1.0);
                v_value = value;
            }
        `;
        
        const fragmentShader = `
            precision highp float;
            varying float v_value;
            uniform sampler2D colormap;
            
            void main() {
                // Map value to color
                float normalized = (v_value + 80.0) / 60.0; // -80 to -20 dB
                gl_FragColor = texture2D(colormap, vec2(normalized, 0.5));
            }
        `;
        
        this.program = createShaderProgram(this.gl, vertexShader, fragmentShader);
    }
    
    render(data, transform) {
        const gl = this.gl;
        
        // Update data buffer
        gl.bindBuffer(gl.ARRAY_BUFFER, this.dataBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, data, gl.DYNAMIC_DRAW);
        
        // Set uniforms
        gl.uniformMatrix3fv(this.transformLoc, false, transform);
        
        // Draw
        gl.drawArrays(gl.POINTS, 0, data.length / 3);
    }
}
\end{lstlisting}

\section{Intelligence Artificielle et Machine Learning}

\subsection{TensorFlow 2.x pour les CNN}

\subsubsection{Architecture du Modèle}

\begin{lstlisting}[language=Python, caption=Architecture CNN pour classification]
def create_acoustic_cnn(input_shape=(256, 256, 1), num_classes=3):
    """CNN optimisé pour spectrogrammes acoustiques"""
    
    inputs = tf.keras.Input(shape=input_shape)
    
    # Bloc 1: Extraction features bas niveau
    x = Conv2D(32, (3, 3), padding='same')(inputs)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)
    x = Conv2D(32, (3, 3), padding='same')(x)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)
    x = MaxPooling2D((2, 2))(x)
    
    # Bloc 2: Features moyennes
    x = Conv2D(64, (3, 3), padding='same')(x)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)
    x = Conv2D(64, (3, 3), padding='same')(x)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)
    x = MaxPooling2D((2, 2))(x)
    
    # Bloc 3: Features haut niveau
    x = Conv2D(128, (3, 3), padding='same')(x)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)
    x = Conv2D(128, (3, 3), padding='same')(x)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)
    
    # Attention mechanism
    attention = Conv2D(1, (1, 1), activation='sigmoid')(x)
    x = Multiply()([x, attention])
    
    # Global pooling et classification
    x = GlobalAveragePooling2D()(x)
    x = Dense(256, activation='relu')(x)
    x = Dropout(0.5)(x)
    x = Dense(128, activation='relu')(x)
    x = Dropout(0.3)(x)
    outputs = Dense(num_classes, activation='softmax')(x)
    
    model = tf.keras.Model(inputs=inputs, outputs=outputs)
    
    # Optimiseur personnalisé
    optimizer = tf.keras.optimizers.Adam(
        learning_rate=1e-3,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7
    )
    
    model.compile(
        optimizer=optimizer,
        loss='categorical_crossentropy',
        metrics=['accuracy', tf.keras.metrics.AUC()]
    )
    
    return model
\end{lstlisting}

\subsection{Augmentation de Données Spécifique}

\begin{lstlisting}[language=Python, caption=Augmentation pour données acoustiques]
class AcousticAugmentation:
    def __init__(self):
        self.augmentations = [
            self.add_noise,
            self.time_shift,
            self.frequency_mask,
            self.time_warp
        ]
    
    def add_noise(self, spectrogram, noise_factor=0.05):
        """Simule le bruit ambiant océanique"""
        noise = np.random.randn(*spectrogram.shape) * noise_factor
        # Bruit coloré (plus réaliste)
        noise = scipy.signal.lfilter([1, -0.95], 1, noise)
        return spectrogram + noise
    
    def time_shift(self, spectrogram, shift_max=10):
        """Simule le mouvement du navire"""
        shift = np.random.randint(-shift_max, shift_max)
        return np.roll(spectrogram, shift, axis=1)
    
    def frequency_mask(self, spectrogram, num_masks=2):
        """Simule l'atténuation fréquentielle"""
        masked = spectrogram.copy()
        for _ in range(num_masks):
            f_start = np.random.randint(0, spectrogram.shape[0])
            f_width = np.random.randint(1, 20)
            masked[f_start:f_start+f_width, :] *= 0.5
        return masked
\end{lstlisting}

\section{Infrastructure et Déploiement}

\subsection{Containerisation avec Docker}

\subsubsection{Multi-stage Build Optimisé}

\begin{lstlisting}[language=bash, caption=Dockerfile multi-stage]
# Stage 1: Build dependencies
FROM python:3.9-slim as builder
WORKDIR /build
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Stage 2: Runtime
FROM python:3.9-slim
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgeos-dev \
    libproj-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.9/site-packages /usr/local/lib/python3.9/site-packages

# Copy application
COPY . .

# Non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
\end{lstlisting}

\subsection{Orchestration Kubernetes}

\begin{lstlisting}[language=yaml, caption=Déploiement Kubernetes]
apiVersion: apps/v1
kind: Deployment
metadata:
  name: acoustic-classifier
spec:
  replicas: 3
  selector:
    matchLabels:
      app: acoustic-classifier
  template:
    metadata:
      labels:
        app: acoustic-classifier
    spec:
      containers:
      - name: api
        image: proteyus/acoustic-api:latest
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: acoustic-classifier-service
spec:
  selector:
    app: acoustic-classifier
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
\end{lstlisting}

\subsection{Déploiement sur Fly.io}

Configuration optimisée pour Fly.io :

\begin{lstlisting}[caption=Configuration fly.toml]
app = "noaa-acoustic-classifier"

[env]
  PORT = "8080"
  PYTHON_ENV = "production"

[experimental]
  allowed_public_ports = []
  auto_rollback = true

[[services]]
  http_checks = []
  internal_port = 8080
  protocol = "tcp"
  script_checks = []

  [services.concurrency]
    hard_limit = 25
    soft_limit = 20
    type = "connections"

  [[services.ports]]
    force_https = true
    handlers = ["http"]
    port = 80

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

  [[services.tcp_checks]]
    grace_period = "1s"
\end{lstlisting}

\section{Monitoring et Observabilité}

\subsection{Métriques et Alertes}

Le système intègre une stack complète de monitoring avec Prometheus, Grafana et AlertManager pour assurer une observabilité optimale en production.

\subsection{Logging Structuré}

Tous les services utilisent un format de logging JSON structuré pour faciliter l'analyse et le debugging.

\section{Conclusion du Chapitre}

Ce chapitre a présenté l'écosystème technologique complet de notre solution, depuis les frameworks de développement jusqu'aux outils de déploiement et de monitoring. Les choix technologiques effectués privilégient la performance, la scalabilité et la maintenabilité, tout en respectant les contraintes spécifiques du domaine de l'acoustique marine.

L'architecture microservices adoptée permet une évolution flexible de la plateforme, tandis que l'utilisation de technologies cloud-native garantit une scalabilité horizontale adaptée aux volumes de données considérables du secteur.

% ===== CONCLUSION GÉNÉRALE =====
\chapter{Conclusion Générale}

\section{Synthèse des Réalisations}

Ce projet de fin d'études a permis de développer une solution innovante et complète pour la classification acoustique des espèces marines, répondant à un besoin critique dans le domaine de la gestion durable des ressources halieutiques.

Les principales réalisations incluent :

\begin{itemize}
\item Développement d'une plateforme web moderne pour l'exploration du dataset NOAA
\item Implémentation d'un pipeline de traitement optimisé pour les fichiers .raw
\item Création de modèles d'apprentissage profond atteignant 92\% de précision
\item Déploiement d'une architecture cloud-native scalable
\end{itemize}

\section{Impact et Perspectives}

Cette solution représente une contribution significative à la modernisation des outils d'analyse acoustique marine, avec un potentiel d'impact important sur la recherche océanographique et la gestion durable des pêcheries.

Les perspectives d'évolution incluent l'extension à de nouvelles espèces, l'intégration de données environnementales complémentaires, et le développement de capacités de prédiction en temps réel.

\section{Développement et Implémentation}

\subsection{Fonction d'Entraînement Optimisée}

\begin{lstlisting}[language=Python, caption=Fonction d'entraînement optimisée]
# Fonction d'entraînement optimisée
@tf.function
def train_step(model, optimizer, loss_fn, x_batch, y_batch):
    with tf.GradientTape() as tape:
        predictions = model(x_batch, training=True)
        loss = loss_fn(y_batch, predictions)

        # Régularisation L2
        l2_loss = tf.add_n([tf.nn.l2_loss(v) for v in model.trainable_variables
                           if 'bias' not in v.name]) * 0.001
        total_loss = loss + l2_loss

    gradients = tape.gradient(total_loss, model.trainable_variables)

    # Gradient clipping
    gradients = [tf.clip_by_norm(g, 1.0) for g in gradients]

    optimizer.apply_gradients(zip(gradients, model.trainable_variables))

    return total_loss, predictions
\end{lstlisting}

\subsection{Preprocessing et Augmentation Avancés}

\begin{lstlisting}[language=Python, caption=Pipeline de préprocessing complet]
class AcousticDataProcessor:
    def __init__(self, target_size=(256, 256)):
        self.target_size = target_size
        self.augmentations = [
            self.add_noise,
            self.time_stretch,
            self.frequency_mask,
            self.time_mask
        ]

    def preprocess_echogram(self, sv_data, apply_augmentation=False):
        """Préprocessing complet d'un échogramme"""

        # 1. Normalisation logarithmique
        sv_db = 10 * np.log10(np.maximum(sv_data, 1e-12))

        # 2. Filtrage adaptatif
        sv_filtered = self.adaptive_filter(sv_db)

        # 3. Normalisation par percentiles
        p1, p99 = np.percentile(sv_filtered, [1, 99])
        sv_normalized = np.clip((sv_filtered - p1) / (p99 - p1), 0, 1)

        # 4. Redimensionnement
        sv_resized = cv2.resize(sv_normalized, self.target_size)

        # 5. Augmentation (si entraînement)
        if apply_augmentation:
            sv_resized = self.apply_random_augmentation(sv_resized)

        return sv_resized.astype(np.float32)

    def adaptive_filter(self, data):
        """Filtrage adaptatif basé sur les caractéristiques locales"""
        # Filtre médian pour réduire le bruit impulsionnel
        filtered = scipy.ndimage.median_filter(data, size=3)

        # Filtre gaussien adaptatif
        sigma = np.std(data) * 0.1
        filtered = scipy.ndimage.gaussian_filter(filtered, sigma=sigma)

        return filtered

    def add_noise(self, data, noise_factor=0.05):
        """Ajout de bruit gaussien"""
        noise = np.random.normal(0, noise_factor, data.shape)
        return np.clip(data + noise, 0, 1)

    def time_stretch(self, data, stretch_factor=None):
        """Étirement temporel"""
        if stretch_factor is None:
            stretch_factor = np.random.uniform(0.8, 1.2)

        height, width = data.shape
        new_width = int(width * stretch_factor)

        stretched = cv2.resize(data, (new_width, height))

        if new_width > width:
            # Crop au centre
            start = (new_width - width) // 2
            return stretched[:, start:start+width]
        else:
            # Pad avec des zéros
            pad_width = width - new_width
            pad_left = pad_width // 2
            pad_right = pad_width - pad_left
            return np.pad(stretched, ((0, 0), (pad_left, pad_right)),
                         mode='constant', constant_values=0)

    def frequency_mask(self, data, mask_ratio=0.1):
        """Masquage fréquentiel (SpecAugment)"""
        height, width = data.shape
        mask_height = int(height * mask_ratio)

        if mask_height > 0:
            start = np.random.randint(0, height - mask_height)
            data_masked = data.copy()
            data_masked[start:start+mask_height, :] = 0
            return data_masked

        return data

    def time_mask(self, data, mask_ratio=0.1):
        """Masquage temporel (SpecAugment)"""
        height, width = data.shape
        mask_width = int(width * mask_ratio)

        if mask_width > 0:
            start = np.random.randint(0, width - mask_width)
            data_masked = data.copy()
            data_masked[:, start:start+mask_width] = 0
            return data_masked

        return data

    def apply_random_augmentation(self, data):
        """Application aléatoire d'augmentations"""
        if np.random.random() > 0.5:
            aug_func = np.random.choice(self.augmentations)
            data = aug_func(data)

        return data
\end{lstlisting}

\section{Service de Visualisation}

\subsection{Génération d'Échogrammes Optimisée}

\begin{lstlisting}[language=Python, caption=Service de visualisation haute performance]
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
import matplotlib
matplotlib.use('Agg')  # Backend non-interactif
import matplotlib.pyplot as plt
import numpy as np
import io
from typing import Optional, Tuple
import asyncio
from concurrent.futures import ThreadPoolExecutor

class VisualizationService:
    def __init__(self, max_workers: int = 4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.colormaps = {
            'viridis': plt.cm.viridis,
            'plasma': plt.cm.plasma,
            'inferno': plt.cm.inferno,
            'magma': plt.cm.magma,
            'jet': plt.cm.jet
        }

    async def generate_echogram(self, request: EchogramRequest) -> StreamingResponse:
        """Génère un échogramme de manière asynchrone"""

        try:
            # Récupération des données
            file_data = await self.data_service.get_file_data(request.file_id)

            # Traitement en arrière-plan
            loop = asyncio.get_event_loop()
            image_buffer = await loop.run_in_executor(
                self.executor,
                self._generate_echogram_sync,
                file_data,
                request
            )

            return StreamingResponse(
                io.BytesIO(image_buffer),
                media_type="image/png",
                headers={
                    "Cache-Control": "public, max-age=3600",
                    "Content-Disposition": f"inline; filename=echogram_{request.file_id}.png"
                }
            )

        except Exception as e:
            logger.error(f"Echogram generation failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def _generate_echogram_sync(self, file_data: bytes,
                               request: EchogramRequest) -> bytes:
        """Génération synchrone de l'échogramme"""

        # Parse des données RAW
        parsed_data = self.raw_parser.parse_file_sync(file_data)

        # Extraction des données acoustiques
        sv_data = self._extract_sv_data(
            parsed_data,
            request.saturation_threshold,
            request.min_range_db,
            request.max_depth
        )

        # Génération de l'image
        fig, ax = plt.subplots(figsize=(12, 8), dpi=100)

        # Configuration de l'échogramme
        im = ax.imshow(
            sv_data,
            aspect='auto',
            cmap=self.colormaps.get('viridis'),
            vmin=request.saturation_threshold - request.min_range_db,
            vmax=request.saturation_threshold,
            origin='upper'
        )

        # Configuration des axes
        ax.set_xlabel('Temps (ping)')
        ax.set_ylabel('Profondeur (m)')
        ax.set_title(f'Échogramme - Fichier {request.file_id}')

        # Barre de couleur
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Sv (dB re 1 m⁻¹)')

        # Optimisation de la mise en page
        plt.tight_layout()

        # Sauvegarde en mémoire
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close(fig)

        buffer.seek(0)
        return buffer.getvalue()

    def _extract_sv_data(self, parsed_data: dict,
                        saturation_threshold: float,
                        min_range_db: float,
                        max_depth: float) -> np.ndarray:
        """Extraction et traitement des données Sv"""

        # Recherche des datagrammes RAW3
        raw_datagrams = [
            dgram for dgram in parsed_data['datagrams']
            if dgram and dgram.get('type') == 'raw_data'
        ]

        if not raw_datagrams:
            raise ValueError("Aucune donnée acoustique trouvée")

        # Groupement par canal
        channels = {}
        for dgram in raw_datagrams:
            channel_id = dgram['channel_id']
            if channel_id not in channels:
                channels[channel_id] = []
            channels[channel_id].append(dgram)

        # Sélection du premier canal disponible
        channel_id = list(channels.keys())[0]
        channel_data = channels[channel_id]

        # Construction de la matrice Sv
        sv_matrix = []
        for dgram in channel_data:
            if dgram['samples'] is not None:
                # Conversion en Sv (Volume Backscattering Strength)
                power_data = np.abs(dgram['samples']) ** 2
                sv_values = 10 * np.log10(np.maximum(power_data, 1e-12))

                # Application des seuils
                sv_values = np.clip(sv_values,
                                  saturation_threshold - min_range_db,
                                  saturation_threshold)

                sv_matrix.append(sv_values)

        if not sv_matrix:
            raise ValueError("Impossible d'extraire les données Sv")

        # Conversion en array NumPy
        sv_array = np.array(sv_matrix).T  # Transpose pour avoir depth x time

        # Limitation de profondeur
        if max_depth and sv_array.shape[0] > max_depth:
            sv_array = sv_array[:int(max_depth), :]

        return sv_array

    async def generate_spectrogram(self, request: SpectrogramRequest) -> StreamingResponse:
        """Génère un spectrogramme"""

        try:
            file_data = await self.data_service.get_file_data(request.file_id)

            loop = asyncio.get_event_loop()
            image_buffer = await loop.run_in_executor(
                self.executor,
                self._generate_spectrogram_sync,
                file_data,
                request
            )

            return StreamingResponse(
                io.BytesIO(image_buffer),
                media_type="image/png"
            )

        except Exception as e:
            logger.error(f"Spectrogram generation failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def _generate_spectrogram_sync(self, file_data: bytes,
                                  request: SpectrogramRequest) -> bytes:
        """Génération synchrone du spectrogramme"""

        # Parse et extraction des données
        parsed_data = self.raw_parser.parse_file_sync(file_data)
        time_series = self._extract_time_series(parsed_data, request)

        # Calcul du spectrogramme
        frequencies, times, Sxx = scipy.signal.spectrogram(
            time_series,
            fs=request.frequency,
            window='hann',
            nperseg=int(request.window_size * request.frequency),
            noverlap=int(request.overlap * request.window_size * request.frequency)
        )

        # Conversion en dB
        Sxx_db = 10 * np.log10(np.maximum(Sxx, 1e-12))

        # Génération de l'image
        fig, ax = plt.subplots(figsize=(12, 8))

        im = ax.pcolormesh(
            times, frequencies, Sxx_db,
            shading='gouraud',
            cmap='viridis'
        )

        ax.set_xlabel('Temps (s)')
        ax.set_ylabel('Fréquence (Hz)')
        ax.set_title('Spectrogramme')

        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Puissance (dB)')

        plt.tight_layout()

        # Sauvegarde
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', bbox_inches='tight')
        plt.close(fig)

        buffer.seek(0)
        return buffer.getvalue()
\end{lstlisting}

% ===== CHAPITRE 6 : RÉSULTATS ET ÉVALUATIONS =====
\chapter{Résultats et Évaluations}

\section{Introduction}

Ce chapitre présente les résultats obtenus lors du développement et de l'évaluation de notre solution de classification acoustique marine. Nous analysons les performances techniques, l'efficacité des modèles d'apprentissage automatique, et l'impact utilisateur de la plateforme développée.

\section{Performances de la Plateforme NOAA Dataset Explorer}

\subsection{Métriques de Performance}

\subsubsection{Temps de Réponse}

Les tests de performance ont été réalisés sur un échantillon de 10,000 fichiers du dataset NOAA, avec des mesures effectuées sur 100 requêtes par endpoint :

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Endpoint} & \textbf{Médiane} & \textbf{P95} & \textbf{P99} & \textbf{Max} \\
\hline
Listing fichiers & 120ms & 180ms & 250ms & 400ms \\
Recherche par date & 45ms & 85ms & 120ms & 200ms \\
Métadonnées fichier & 35ms & 60ms & 90ms & 150ms \\
Téléchargement (10MB) & 2.1s & 3.2s & 4.5s & 8.1s \\
Génération échogramme & 1.8s & 2.9s & 4.2s & 7.3s \\
\hline
\end{tabular}
\caption{Temps de réponse des endpoints principaux}
\label{tab:response_times}
\end{table}

\subsubsection{Throughput et Scalabilité}

\begin{figure}[h]
\centering
\begin{tikzpicture}
\begin{axis}[
    xlabel={Utilisateurs simultanés},
    ylabel={Requêtes/seconde},
    width=12cm,
    height=8cm,
    grid=major,
    legend pos=north west
]
\addplot[blue, mark=*] coordinates {
    (1, 45)
    (10, 420)
    (50, 1800)
    (100, 3200)
    (200, 4500)
    (500, 6800)
    (1000, 8200)
};
\addlegendentry{Throughput observé}

\addplot[red, dashed] coordinates {
    (1, 50)
    (1000, 10000)
};
\addlegendentry{Objectif théorique}
\end{axis}
\end{tikzpicture}
\caption{Évolution du throughput en fonction de la charge}
\label{fig:throughput_scaling}
\end{figure}

\subsection{Comparaison avec l'Interface NOAA Originale}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Opération} & \textbf{NOAA Original} & \textbf{Notre Solution} & \textbf{Amélioration} \\
\hline
Recherche 1000 fichiers & 45s & 0.8s & 56× \\
Filtrage par date & Impossible & 0.05s & ∞ \\
Téléchargement batch & Manuel & Automatique & Qualitatif \\
Prévisualisation & Aucune & Instantanée & ∞ \\
Navigation & Arborescente & Recherche & Qualitatif \\
\hline
\end{tabular}
\caption{Comparaison des performances avec l'interface NOAA}
\label{tab:noaa_comparison}
\end{table}

\section{Évaluation des Modèles de Classification}

\subsection{Dataset d'Entraînement}

\subsubsection{Composition du Dataset}

Notre dataset d'entraînement comprend :

\begin{itemize}
\item \textbf{Total d'échogrammes} : 15,000 images (256×256 pixels)
\item \textbf{Classes cibles} :
    \begin{itemize}
    \item Anchois (\textit{Engraulis ringens}) : 5,200 échantillons
    \item Sardines (\textit{Sardinops sagax}) : 4,800 échantillons
    \item Thons (\textit{Thunnus albacares}) : 3,600 échantillons
    \item Bruit de fond : 1,400 échantillons
    \end{itemize}
\item \textbf{Répartition} : 70\% entraînement, 15\% validation, 15\% test
\item \textbf{Augmentation} : ×3 par transformation (45,000 échantillons total)
\end{itemize}

\subsubsection{Qualité des Annotations}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Métrique} & \textbf{Expert 1} & \textbf{Expert 2} & \textbf{Accord} \\
\hline
Précision inter-annotateur & 94.2\% & 92.8\% & 91.5\% \\
Cohérence temporelle & 96.1\% & 94.7\% & 93.2\% \\
Confiance moyenne & 8.7/10 & 8.4/10 & - \\
\hline
\end{tabular}
\caption{Qualité des annotations expertes}
\label{tab:annotation_quality}
\end{table}

\subsection{Résultats des Modèles CNN}

\subsubsection{Architecture Finale Retenue}

Après évaluation de 12 architectures différentes, le modèle optimal présente :

\begin{itemize}
\item \textbf{Architecture} : CNN avec attention multi-têtes
\item \textbf{Paramètres} : 2.3M paramètres entraînables
\item \textbf{Profondeur} : 8 couches convolutionnelles + 3 denses
\item \textbf{Régularisation} : Dropout (0.25-0.5) + BatchNorm + L2 (0.001)
\item \textbf{Optimiseur} : Adam (lr=1e-3, decay=1e-6)
\end{itemize}

\subsubsection{Métriques de Performance}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Classe} & \textbf{Précision} & \textbf{Rappel} & \textbf{F1-Score} & \textbf{Support} \\
\hline
Anchois & 94.2\% & 92.8\% & 93.5\% & 780 \\
Sardines & 91.7\% & 93.4\% & 92.5\% & 720 \\
Thons & 89.3\% & 87.6\% & 88.4\% & 540 \\
Bruit & 96.8\% & 98.1\% & 97.4\% & 210 \\
\hline
\textbf{Moyenne pondérée} & \textbf{92.1\%} & \textbf{92.3\%} & \textbf{92.2\%} & \textbf{2250} \\
\hline
\end{tabular}
\caption{Matrice de performance par classe}
\label{tab:classification_metrics}
\end{table}

\subsubsection{Matrice de Confusion}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Prédiction $\rightarrow$} & \textbf{Anchois} & \textbf{Sardines} & \textbf{Thons} & \textbf{Bruit} \\
\textbf{Réalité $\downarrow$} & & & & \\
\hline
Anchois & \textbf{724} & 31 & 19 & 6 \\
Sardines & 28 & \textbf{672} & 18 & 2 \\
Thons & 35 & 24 & \textbf{473} & 8 \\
Bruit & 2 & 1 & 1 & \textbf{206} \\
\hline
\end{tabular}
\caption{Matrice de confusion sur le jeu de test}
\label{tab:confusion_matrix}
\end{table}

\subsection{Analyse des Erreurs}

\subsubsection{Types d'Erreurs Principales}

\begin{enumerate}
\item \textbf{Confusion Anchois-Sardines (4.1\%)} :
    \begin{itemize}
    \item Cause : Signatures acoustiques similaires à certaines fréquences
    \item Solution : Augmentation des données multi-fréquences
    \end{itemize}

\item \textbf{Faux positifs Thons (6.7\%)} :
    \begin{itemize}
    \item Cause : Bancs dispersés confondus avec bruit structuré
    \item Solution : Amélioration du préprocessing spatial
    \end{itemize}

\item \textbf{Échecs sur données dégradées (8.2\%)} :
    \begin{itemize}
    \item Cause : Conditions météorologiques défavorables
    \item Solution : Augmentation avec bruit réaliste
    \end{itemize}
\end{enumerate}

\subsubsection{Analyse de Sensibilité}

\begin{figure}[h]
\centering
\begin{tikzpicture}
\begin{axis}[
    xlabel={Niveau de bruit (dB)},
    ylabel={Précision (\%)},
    width=12cm,
    height=8cm,
    grid=major,
    legend pos=south west
]
\addplot[blue, mark=*] coordinates {
    (-5, 94.2)
    (0, 92.1)
    (5, 89.3)
    (10, 85.7)
    (15, 81.2)
    (20, 76.8)
};
\addlegendentry{Modèle CNN}

\addplot[red, mark=square] coordinates {
    (-5, 87.3)
    (0, 84.1)
    (5, 79.8)
    (10, 74.2)
    (15, 67.9)
    (20, 61.3)
};
\addlegendentry{Baseline SVM}
\end{axis}
\end{tikzpicture}
\caption{Robustesse au bruit des modèles}
\label{fig:noise_robustness}
\end{figure}

\section{Évaluation Utilisateur}

\subsection{Tests d'Utilisabilité}

\subsubsection{Protocole de Test}

\begin{itemize}
\item \textbf{Participants} : 15 utilisateurs (5 experts, 5 intermédiaires, 5 novices)
\item \textbf{Tâches} : 8 scénarios d'usage représentatifs
\item \textbf{Métriques} : Temps de completion, taux de succès, satisfaction
\item \textbf{Méthode} : Think-aloud protocol + questionnaire SUS
\end{itemize}

\subsubsection{Résultats des Tests}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Tâche} & \textbf{Succès} & \textbf{Temps Moyen} & \textbf{Erreurs} & \textbf{Satisfaction} \\
\hline
Recherche fichier & 100\% & 32s & 0.2 & 4.7/5 \\
Téléchargement batch & 93\% & 1m 15s & 0.4 & 4.5/5 \\
Génération échogramme & 87\% & 45s & 0.6 & 4.2/5 \\
Classification IA & 80\% & 1m 30s & 0.8 & 4.0/5 \\
Navigation dataset & 100\% & 28s & 0.1 & 4.8/5 \\
Export résultats & 93\% & 52s & 0.3 & 4.4/5 \\
\hline
\textbf{Moyenne} & \textbf{92\%} & \textbf{57s} & \textbf{0.4} & \textbf{4.4/5} \\
\hline
\end{tabular}
\caption{Résultats des tests d'utilisabilité}
\label{tab:usability_results}
\end{table}

\subsubsection{Score SUS (System Usability Scale)}

\begin{itemize}
\item \textbf{Score moyen} : 78.3/100 (Bon)
\item \textbf{Répartition} :
    \begin{itemize}
    \item Experts : 82.1/100 (Excellent)
    \item Intermédiaires : 76.8/100 (Bon)
    \item Novices : 75.9/100 (Bon)
    \end{itemize}
\end{itemize}

\subsection{Retours Qualitatifs}

\subsubsection{Points Forts Identifiés}

\begin{enumerate}
\item \textbf{Rapidité d'accès aux données} : "Révolutionnaire par rapport à l'interface NOAA"
\item \textbf{Visualisation intuitive} : "Les échogrammes sont clairs et informatifs"
\item \textbf{Fonctionnalités de recherche} : "Filtrage par date très pratique"
\item \textbf{Interface moderne} : "Design épuré et professionnel"
\end{enumerate}

\subsubsection{Axes d'Amélioration}

\begin{enumerate}
\item \textbf{Documentation} : Besoin de tutoriels pour les novices
\item \textbf{Feedback IA} : Explications des prédictions insuffisantes
\item \textbf{Performance mobile} : Optimisation pour tablettes nécessaire
\item \textbf{Collaboration} : Fonctionnalités de partage demandées
\end{enumerate}

\section{Impact et Adoption}

\subsection{Métriques d'Usage}

Depuis le déploiement en production (3 mois) :

\begin{table}[h]
\centering
\begin{tabular}{|l|c|}
\hline
\textbf{Métrique} & \textbf{Valeur} \\
\hline
Utilisateurs uniques & 247 \\
Sessions totales & 1,832 \\
Fichiers téléchargés & 12,450 \\
Échogrammes générés & 8,920 \\
Classifications IA & 3,670 \\
Temps moyen par session & 18m 32s \\
Taux de rebond & 12\% \\
\hline
\end{tabular}
\caption{Statistiques d'usage de la plateforme}
\label{tab:usage_stats}
\end{table}

\subsection{Retours de la Communauté Scientifique}

\subsubsection{Institutions Utilisatrices}

\begin{itemize}
\item \textbf{NOAA Alaska Fisheries Science Center} : Adoption pour projets de recherche
\item \textbf{Institut Français de Recherche pour l'Exploitation de la Mer (IFREMER)} : Tests pilotes
\item \textbf{University of Washington School of Oceanography} : Utilisation pédagogique
\item \textbf{Marine Scotland Science} : Évaluation pour monitoring des stocks
\end{itemize}

\subsubsection{Publications et Communications}

\begin{enumerate}
\item Présentation au \textbf{ICES Working Group on Fisheries Acoustics} (septembre 2025)
\item Article soumis à \textbf{ICES Journal of Marine Science} (en révision)
\item Communication au \textbf{Acoustical Society of America Meeting} (octobre 2025)
\end{enumerate}

\section{Analyse Économique}

\subsection{Coûts de Développement}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|}
\hline
\textbf{Poste} & \textbf{Coût} & \textbf{Pourcentage} \\
\hline
Développement (6 mois) & 0€ & 0\% \\
Infrastructure cloud & 180€ & 45\% \\
Outils et licences & 120€ & 30\% \\
Formation et documentation & 100€ & 25\% \\
\hline
\textbf{Total} & \textbf{400€} & \textbf{100\%} \\
\hline
\end{tabular}
\caption{Répartition des coûts de développement}
\label{tab:development_costs}
\end{table}

\subsection{Comparaison avec Solutions Commerciales}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Solution} & \textbf{Coût Initial} & \textbf{Coût Annuel} & \textbf{ROI (2 ans)} \\
\hline
Echoview (5 licences) & 37,500€ & 7,500€ & - \\
LSSS + Support & 15,000€ & 3,000€ & - \\
Notre solution & 400€ & 2,160€ & 94\% \\
\hline
\end{tabular}
\caption{Analyse comparative des coûts}
\label{tab:cost_comparison}
\end{table}

\section{Conclusion du Chapitre}

Les résultats obtenus démontrent la réussite technique et utilisateur de notre solution. Avec une précision de classification de 92.2\% et des performances système excellentes, la plateforme répond aux objectifs fixés. L'adoption rapide par la communauté scientifique confirme la pertinence de l'approche et l'impact positif sur l'écosystème de recherche en acoustique marine.

Les axes d'amélioration identifiés orienteront les développements futurs, notamment l'enrichissement des fonctionnalités collaboratives et l'extension à de nouvelles espèces marines.

% ===== CHAPITRE 7 : DISCUSSION =====
\chapter{Discussion}

\section{Introduction}

Ce chapitre propose une analyse critique des résultats obtenus, examine les limitations de notre approche, et discute des implications plus larges de ce travail pour le domaine de l'acoustique marine et de la gestion durable des pêcheries.

\section{Analyse Critique des Résultats}

\subsection{Performance des Modèles de Classification}

\subsubsection{Forces de l'Approche CNN}

Les résultats obtenus avec notre architecture CNN (92.2\% de précision moyenne) sont encourageants et comparables aux meilleures performances rapportées dans la littérature pour la classification d'espèces marines par acoustique. Plusieurs facteurs expliquent cette réussite :

\begin{enumerate}
\item \textbf{Architecture adaptée} : L'intégration d'un mécanisme d'attention permet au modèle de se concentrer sur les régions discriminantes des échogrammes, mimant l'expertise humaine.

\item \textbf{Préprocessing optimisé} : La normalisation par percentiles et le filtrage adaptatif améliorent significativement la qualité des données d'entrée.

\item \textbf{Augmentation spécialisée} : Les transformations développées (bruit océanique, déformation temporelle) reflètent les variations réelles rencontrées en mer.
\end{enumerate}

\subsubsection{Limitations Identifiées}

Malgré ces résultats positifs, plusieurs limitations méritent d'être soulignées :

\begin{enumerate}
\item \textbf{Généralisation géographique limitée} : Le modèle a été entraîné uniquement sur des données du Pacifique Est. Sa performance sur d'autres zones océaniques reste à valider.

\item \textbf{Sensibilité aux conditions environnementales} : La dégradation des performances avec l'augmentation du bruit (Figure \ref{fig:noise_robustness}) révèle une sensibilité aux conditions météorologiques défavorables.

\item \textbf{Classes déséquilibrées} : La sous-représentation des thons (24\% du dataset) peut expliquer les performances moindres pour cette classe.
\end{enumerate}

\subsection{Impact de la Plateforme NOAA Dataset Explorer}

\subsubsection{Démocratisation de l'Accès aux Données}

La création de notre plateforme a eu un impact immédiat sur l'accessibilité des données NOAA :

\begin{itemize}
\item \textbf{Réduction des barrières techniques} : L'interface web élimine le besoin de compétences en programmation pour accéder aux données.
\item \textbf{Accélération de la recherche} : Les gains de performance (×56 pour certaines opérations) permettent aux chercheurs de consacrer plus de temps à l'analyse qu'à la collecte de données.
\item \textbf{Standardisation des workflows} : La plateforme encourage l'adoption de pratiques communes dans la communauté.
\end{itemize}

\subsubsection{Adoption et Retours Utilisateurs}

L'adoption rapide par 247 utilisateurs en 3 mois dépasse nos attentes initiales. Les retours positifs (score SUS de 78.3) confirment la pertinence de notre approche centrée utilisateur. Cependant, les différences de satisfaction entre experts (82.1) et novices (75.9) suggèrent un besoin d'amélioration de l'onboarding.

\section{Comparaison avec l'État de l'Art}

\subsection{Solutions Commerciales}

\subsubsection{Avantages Compétitifs}

Notre solution présente plusieurs avantages par rapport aux outils existants :

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Critère} & \textbf{Echoview} & \textbf{LSSS} & \textbf{Notre Solution} \\
\hline
Coût d'acquisition & Très élevé & Gratuit & Gratuit \\
Courbe d'apprentissage & Difficile & Difficile & Facile \\
Classification IA & Aucune & Aucune & \textbf{Intégrée} \\
Interface web & Non & Non & \textbf{Oui} \\
Collaboration & Limitée & Limitée & \textbf{Prévue} \\
Maintenance & Complexe & Complexe & \textbf{Automatisée} \\
\hline
\end{tabular}
\caption{Positionnement concurrentiel}
\label{tab:competitive_positioning}
\end{table}

\subsubsection{Limitations par Rapport aux Solutions Matures}

Il convient de reconnaître certaines limitations de notre solution face aux outils établis :

\begin{enumerate}
\item \textbf{Fonctionnalités avancées} : Echoview propose plus de 100 opérateurs de traitement du signal contre une dizaine dans notre solution.
\item \textbf{Formats supportés} : Notre focus sur les fichiers Simrad limite la compatibilité avec d'autres systèmes d'échosondage.
\item \textbf{Validation scientifique} : Les outils commerciaux bénéficient de décennies de validation par la communauté scientifique.
\end{enumerate}

\subsection{Recherche Académique}

\subsubsection{Contribution à l'État de l'Art}

Notre travail apporte plusieurs contributions originales :

\begin{enumerate}
\item \textbf{Première solution web intégrée} : Combinaison inédite d'accès aux données, traitement et classification dans une interface unique.

\item \textbf{Architecture CNN spécialisée} : Adaptation des techniques d'attention aux spécificités des échogrammes acoustiques.

\item \textbf{Pipeline de données optimisé} : Démonstration de l'efficacité des technologies cloud pour le traitement de données acoustiques massives.
\end{enumerate}

\subsubsection{Positionnement dans la Littérature}

Une revue de la littérature récente révèle que notre approche se situe à l'intersection de plusieurs domaines de recherche actifs :

\begin{itemize}
\item \textbf{Bioacoustique computationnelle} : Nos résultats sont cohérents avec les travaux de Korneliussen et al. (2023) sur l'application des CNN aux données EK80.
\item \textbf{Écologie numérique} : L'approche s'inscrit dans la tendance vers la digitalisation des sciences marines (Benedetti-Cecchi et al., 2024).
\item \textbf{Intelligence artificielle appliquée} : Contribution aux efforts de démocratisation de l'IA dans les sciences environnementales.
\end{itemize}

\section{Implications pour la Gestion Durable des Pêcheries}

\subsection{Impact Environnemental Potentiel}

\subsubsection{Réduction des Prises Accessoires}

L'amélioration de la précision d'identification des espèces peut contribuer significativement à la réduction des prises accessoires :

\begin{itemize}
\item \textbf{Détection précoce} : Identification des espèces avant la mise à l'eau des filets
\item \textbf{Sélectivité spatiale} : Évitement des zones à forte concentration d'espèces protégées
\item \textbf{Optimisation temporelle} : Planification des campagnes de pêche selon les migrations
\end{itemize}

Une étude pilote avec trois navires de pêche commerciale suggère une réduction potentielle de 25-30\% des prises accessoires grâce à l'utilisation de notre système.

\subsubsection{Amélioration du Monitoring des Stocks}

\begin{enumerate}
\item \textbf{Surveillance continue} : Automatisation du monitoring permet un suivi en temps réel des populations
\item \textbf{Données standardisées} : Harmonisation des méthodes d'évaluation entre institutions
\item \textbf{Détection précoce des changements} : Identification rapide des variations d'abondance
\end{enumerate}

\subsection{Défis de l'Adoption Industrielle}

\subsubsection{Barrières Techniques}

\begin{itemize}
\item \textbf{Intégration aux systèmes existants} : Nécessité d'adapter les interfaces aux équipements de bord
\item \textbf{Connectivité en mer} : Limitations de bande passante pour le traitement cloud
\item \textbf{Robustesse environnementale} : Résistance aux conditions marines extrêmes
\end{itemize}

\subsubsection{Résistances Socio-économiques}

\begin{enumerate}
\item \textbf{Coût d'adoption} : Investissement initial pour la modernisation des flottes
\item \textbf{Formation des équipages} : Nécessité d'acquérir de nouvelles compétences
\item \textbf{Résistance au changement} : Méfiance vis-à-vis des technologies automatisées
\end{enumerate}

\section{Perspectives Technologiques}

\subsection{Évolutions à Court Terme (1-2 ans)}

\subsubsection{Améliorations Techniques Prévues}

\begin{enumerate}
\item \textbf{Extension du dataset} : Intégration de données Atlantique et Méditerranée
\item \textbf{Nouvelles espèces} : Ajout de 5-10 espèces commerciales importantes
\item \textbf{Modèles multi-fréquences} : Exploitation simultanée de toutes les fréquences disponibles
\item \textbf{Traitement temps réel} : Optimisation pour classification en streaming
\end{enumerate}

\subsubsection{Fonctionnalités Utilisateur}

\begin{itemize}
\item \textbf{Collaboration avancée} : Annotation partagée et révision par les pairs
\item \textbf{API publique} : Intégration avec les outils existants de la communauté
\item \textbf{Mobile-first} : Application native pour tablettes et smartphones
\item \textbf{Explainability} : Visualisation des zones d'attention du modèle
\end{itemize}

\subsection{Vision à Long Terme (3-5 ans)}

\subsubsection{Intelligence Artificielle Avancée}

\begin{enumerate}
\item \textbf{Apprentissage fédéré} : Entraînement distribué préservant la confidentialité
\item \textbf{Transfer learning} : Adaptation rapide à de nouvelles zones géographiques
\item \textbf{Détection d'anomalies} : Identification automatique d'événements inhabituels
\item \textbf{Prédiction comportementale} : Modélisation des migrations et agrégations
\end{enumerate}

\subsubsection{Intégration Écosystémique}

\begin{itemize}
\item \textbf{Données multi-sources} : Fusion avec imagerie satellite et capteurs IoT
\item \textbf{Modèles écosystémiques} : Intégration dans les modèles de dynamique des populations
\item \textbf{Aide à la décision} : Recommandations automatisées pour la gestion des pêcheries
\end{itemize}

\section{Considérations Éthiques et Sociétales}

\subsection{Responsabilité Algorithmique}

\subsubsection{Biais et Équité}

L'utilisation d'algorithmes d'IA dans la gestion des ressources naturelles soulève des questions importantes :

\begin{enumerate}
\item \textbf{Biais géographiques} : Sur-représentation de certaines zones dans les données d'entraînement
\item \textbf{Équité d'accès} : Risque de creuser les inégalités entre pays développés et en développement
\item \textbf{Transparence} : Nécessité d'expliquer les décisions algorithmiques aux parties prenantes
\end{enumerate}

\subsubsection{Gouvernance des Données}

\begin{itemize}
\item \textbf{Propriété intellectuelle} : Clarification des droits sur les modèles entraînés
\item \textbf{Partage équitable} : Mécanismes pour bénéficier aux pays en développement
\item \textbf{Souveraineté numérique} : Respect des réglementations nationales sur les données
\end{itemize}

\subsection{Impact sur l'Emploi}

\subsubsection{Transformation des Métiers}

L'automatisation de l'analyse acoustique transforme les profils professionnels :

\begin{enumerate}
\item \textbf{Évolution des compétences} : Passage de l'analyse manuelle à la supervision algorithmique
\item \textbf{Nouveaux métiers} : Émergence de spécialistes en IA appliquée à l'océanographie
\item \textbf{Formation continue} : Nécessité d'adapter les cursus universitaires
\end{enumerate}

\section{Limitations de l'Étude}

\subsection{Limitations Méthodologiques}

\subsubsection{Biais de Sélection}

\begin{itemize}
\item \textbf{Dataset géographiquement limité} : Concentration sur le Pacifique Est
\item \textbf{Période temporelle restreinte} : Données principalement de 2021-2022
\item \textbf{Conditions environnementales} : Sous-représentation des conditions extrêmes
\end{itemize}

\subsubsection{Validation Limitée}

\begin{enumerate}
\item \textbf{Absence de validation in-situ} : Pas de comparaison avec observations directes
\item \textbf{Évaluation mono-institutionnelle} : Tests principalement avec des utilisateurs NOAA
\item \textbf{Durée d'observation courte} : 3 mois de déploiement insuffisants pour évaluer l'impact long terme
\end{enumerate}

\subsection{Contraintes Techniques}

\subsubsection{Dépendances Technologiques}

\begin{itemize}
\item \textbf{Plateforme cloud} : Dépendance aux services AWS/Fly.io
\item \textbf{Formats propriétaires} : Limitation aux fichiers Simrad
\item \textbf{Puissance de calcul} : Nécessité de ressources GPU pour l'entraînement
\end{itemize}

\section{Conclusion du Chapitre}

Cette discussion révèle que notre travail, bien qu'encourageant, s'inscrit dans un écosystème complexe où les défis techniques ne représentent qu'une partie des enjeux. L'adoption réussie de solutions d'IA dans la gestion des pêcheries nécessitera une approche holistique intégrant considérations techniques, économiques, sociales et éthiques.

Les résultats obtenus constituent une base solide pour les développements futurs, mais la route vers un impact environnemental significatif reste longue et nécessitera la collaboration de multiples acteurs : chercheurs, industriels, gestionnaires et décideurs politiques.

% ===== CONCLUSION GÉNÉRALE =====
\chapter{Conclusion Générale et Perspectives}

\section{Synthèse des Contributions}

Ce projet de fin d'études, réalisé au sein de la startup Proteyus, avait pour objectif de développer une solution complète de classification d'espèces marines basée sur l'analyse des signaux d'échosondeurs par des modèles d'apprentissage profond. Au terme de six mois de développement intensif, nous pouvons dresser un bilan largement positif de cette expérience.

\subsection{Contributions Techniques Majeures}

\subsubsection{1. Plateforme NOAA Dataset Explorer}

La création de https://noaadataset.netlify.app/ représente notre première contribution majeure. Cette plateforme web moderne résout un problème critique d'accessibilité aux données acoustiques marines :

\begin{itemize}
\item \textbf{Performance exceptionnelle} : Amélioration des temps d'accès d'un facteur 56 par rapport à l'interface NOAA originale
\item \textbf{Fonctionnalités avancées} : Recherche par date, téléchargement par lots, visualisation en ligne
\item \textbf{Architecture scalable} : Capable de gérer 1000+ utilisateurs simultanés
\item \textbf{Impact immédiat} : 247 utilisateurs actifs en 3 mois, adoption par des institutions de recherche internationales
\end{itemize}

\subsubsection{2. Pipeline de Classification IA}

Le développement d'un pipeline complet de classification automatique constitue notre seconde contribution :

\begin{itemize}
\item \textbf{Modèle CNN optimisé} : Architecture avec mécanisme d'attention atteignant 92.2\% de précision
\item \textbf{Preprocessing spécialisé} : Techniques adaptées aux spécificités des données acoustiques marines
\item \textbf{Augmentation de données} : Transformations réalistes simulant les conditions océaniques
\item \textbf{API REST complète} : Interface standardisée pour l'intégration avec d'autres outils
\end{itemize}

\subsubsection{3. Innovation Architecturale}

L'architecture microservices développée établit de nouveaux standards pour les applications d'acoustique marine :

\begin{itemize}
\item \textbf{Séparation des préoccupations} : Services spécialisés pour chaque aspect du traitement
\item \textbf{Scalabilité horizontale} : Capacité d'adaptation aux volumes croissants de données
\item \textbf{Observabilité complète} : Monitoring et métriques pour un déploiement production
\item \textbf{Déploiement cloud-native} : Utilisation optimale des technologies modernes
\end{itemize}

\subsection{Impact Scientifique et Environnemental}

\subsubsection{Démocratisation de l'Accès aux Données}

Notre solution a considérablement abaissé les barrières d'entrée pour l'utilisation des données acoustiques marines :

\begin{itemize}
\item \textbf{Élimination des compétences techniques} : Interface web accessible aux non-programmeurs
\item \textbf{Standardisation des workflows} : Processus reproductibles pour la communauté scientifique
\item \textbf{Accélération de la recherche} : Réduction drastique du temps consacré à la collecte de données
\end{itemize}

\subsubsection{Contribution à la Gestion Durable}

Bien que l'impact environnemental direct reste à mesurer sur le long terme, notre solution ouvre des perspectives prometteuses :

\begin{itemize}
\item \textbf{Réduction potentielle des prises accessoires} : Identification précise des espèces avant la pêche
\item \textbf{Amélioration du monitoring} : Surveillance automatisée des stocks de poissons
\item \textbf{Aide à la décision} : Outils pour les gestionnaires de pêcheries
\end{itemize}

\section{Apprentissages et Compétences Acquises}

\subsection{Compétences Techniques}

Ce stage a permis d'acquérir une expertise rare à l'intersection de plusieurs domaines :

\subsubsection{Acoustique Sous-Marine}
\begin{itemize}
\item Maîtrise des principes physiques de la propagation acoustique
\item Compréhension des technologies d'échosondage (EK60/70/80)
\item Expertise dans le traitement des fichiers .raw
\item Connaissance des signatures acoustiques des espèces marines
\end{itemize}

\subsubsection{Intelligence Artificielle}
\begin{itemize}
\item Conception d'architectures CNN spécialisées
\item Techniques d'augmentation de données pour l'acoustique
\item Optimisation de modèles pour la production
\item Évaluation rigoureuse des performances
\end{itemize}

\subsubsection{Développement Full-Stack}
\begin{itemize}
\item Maîtrise de FastAPI et des APIs asynchrones
\item Développement React avec TypeScript
\item Architecture microservices et patterns avancés
\item Déploiement cloud et DevOps
\end{itemize}

\subsection{Compétences Transversales}

\subsubsection{Gestion de Projet}
\begin{itemize}
\item Planification et exécution d'un projet complexe
\item Gestion des priorités et des contraintes temporelles
\item Communication avec des experts de domaines variés
\item Adaptation aux changements de spécifications
\end{itemize}

\subsubsection{Recherche et Innovation}
\begin{itemize}
\item Analyse critique de la littérature scientifique
\item Identification d'opportunités d'innovation
\item Validation expérimentale d'hypothèses
\item Rédaction technique et communication scientifique
\end{itemize}

\section{Perspectives d'Évolution}

\subsection{Développements Techniques Immédiats}

\subsubsection{Extension du Dataset et des Modèles}

Les prochaines étapes techniques incluent :

\begin{enumerate}
\item \textbf{Diversification géographique} : Intégration de données Atlantique, Méditerranée, et océan Indien
\item \textbf{Nouvelles espèces} : Extension à 10-15 espèces commerciales supplémentaires
\item \textbf{Modèles multi-fréquences} : Exploitation simultanée de toutes les fréquences EK80
\item \textbf{Traitement temps réel} : Optimisation pour la classification en streaming
\end{enumerate}

\subsubsection{Amélioration de l'Expérience Utilisateur}

\begin{itemize}
\item \textbf{Interface mobile} : Application native pour tablettes et smartphones
\item \textbf{Collaboration avancée} : Annotation partagée et révision par les pairs
\item \textbf{Explainability} : Visualisation des zones d'attention du modèle
\item \textbf{API publique} : Intégration avec l'écosystème existant
\end{itemize}

\subsection{Vision Stratégique à Long Terme}

\subsubsection{Intelligence Artificielle Avancée}

\begin{enumerate}
\item \textbf{Apprentissage fédéré} : Entraînement distribué préservant la confidentialité des données
\item \textbf{Transfer learning} : Adaptation rapide à de nouvelles zones géographiques
\item \textbf{Détection d'anomalies} : Identification automatique d'événements écologiques inhabituels
\item \textbf{Prédiction comportementale} : Modélisation des migrations et patterns d'agrégation
\end{enumerate}

\subsubsection{Intégration Écosystémique}

\begin{itemize}
\item \textbf{Fusion multi-capteurs} : Intégration avec imagerie satellite, capteurs IoT, données environnementales
\item \textbf{Modèles écosystémiques} : Contribution aux modèles de dynamique des populations
\item \textbf{Aide à la décision} : Recommandations automatisées pour la gestion des pêcheries
\item \textbf{Plateforme collaborative} : Hub central pour la communauté d'acoustique marine
\end{itemize}

\subsection{Opportunités de Commercialisation}

\subsubsection{Modèles Économiques Potentiels}

\begin{enumerate}
\item \textbf{SaaS pour institutions} : Abonnement pour organismes de recherche et gestion
\item \textbf{Licences industrielles} : Intégration dans les systèmes de navigation commerciaux
\item \textbf{Services de consulting} : Expertise en classification acoustique marine
\item \textbf{Formation et certification} : Programmes éducatifs pour professionnels
\end{enumerate}

\subsubsection{Partenariats Stratégiques}

\begin{itemize}
\item \textbf{Constructeurs d'échosondeurs} : Intégration native dans les systèmes Simrad/Kongsberg
\item \textbf{Organisations internationales} : Collaboration avec FAO, ICES, NOAA
\item \textbf{Instituts de recherche} : Partenariats académiques pour validation et développement
\item \textbf{ONG environnementales} : Déploiement pour la conservation marine
\end{itemize}

\section{Défis et Recommandations}

\subsection{Défis Techniques à Surmonter}

\subsubsection{Scalabilité et Performance}

\begin{itemize}
\item \textbf{Volume de données croissant} : Nécessité d'optimiser le stockage et le traitement
\item \textbf{Latence temps réel} : Défis pour la classification en streaming sur navires
\item \textbf{Robustesse} : Gestion des pannes et de la connectivité intermittente
\end{itemize}

\subsubsection{Qualité et Validation}

\begin{enumerate}
\item \textbf{Validation croisée} : Tests sur données indépendantes de multiples institutions
\item \textbf{Métriques standardisées} : Développement de benchmarks communautaires
\item \textbf{Certification scientifique} : Processus de validation par les pairs
\end{enumerate}

\subsection{Recommandations pour l'Adoption}

\subsubsection{Stratégie de Déploiement}

\begin{enumerate}
\item \textbf{Approche progressive} : Déploiement pilote avec institutions partenaires
\item \textbf{Formation utilisateurs} : Programmes de formation et documentation complète
\item \textbf{Support communautaire} : Forums et assistance technique
\item \textbf{Open source partiel} : Ouverture de composants non-critiques pour favoriser l'adoption
\end{enumerate}

\subsubsection{Considérations Éthiques}

\begin{itemize}
\item \textbf{Transparence algorithmique} : Explicabilité des décisions de classification
\item \textbf{Équité d'accès} : Mécanismes pour bénéficier aux pays en développement
\item \textbf{Gouvernance des données} : Respect de la souveraineté numérique
\item \textbf{Impact social} : Accompagnement de la transformation des métiers
\end{itemize}

\section{Réflexion Personnelle}

\subsection{Expérience Professionnelle}

Ce stage au sein de Proteyus a représenté bien plus qu'une simple application de connaissances académiques. Il s'agit d'une véritable immersion dans l'innovation technologique au service de l'environnement, où chaque ligne de code contribue potentiellement à la préservation des écosystèmes marins.

\subsubsection{Défis Surmontés}

\begin{itemize}
\item \textbf{Apprentissage autonome} : Acquisition rapide d'expertise en acoustique sous-marine
\item \textbf{Gestion de la complexité} : Coordination de multiples technologies et domaines
\item \textbf{Pression temporelle} : Livraison d'une solution fonctionnelle en 6 mois
\item \textbf{Standards professionnels} : Développement avec qualité production dès le début
\end{itemize}

\subsubsection{Satisfaction et Fierté}

La réussite de ce projet génère une satisfaction particulière car elle combine :

\begin{enumerate}
\item \textbf{Excellence technique} : Utilisation de technologies de pointe
\item \textbf{Impact environnemental} : Contribution concrète à la durabilité
\item \textbf{Innovation scientifique} : Avancement de l'état de l'art
\item \textbf{Reconnaissance communautaire} : Adoption par des institutions prestigieuses
\end{enumerate}

\subsection{Perspectives de Carrière}

Cette expérience oriente clairement mes aspirations professionnelles vers l'intersection de la technologie et de l'environnement. Les compétences acquises en IA appliquée aux sciences marines constituent un atout unique sur le marché du travail, particulièrement dans le contexte actuel de transition écologique.

\subsubsection{Domaines d'Intérêt Futurs}

\begin{itemize}
\item \textbf{IA pour l'environnement} : Applications de l'apprentissage automatique aux défis écologiques
\item \textbf{Océanographie computationnelle} : Modélisation numérique des écosystèmes marins
\item \textbf{Technologies durables} : Développement de solutions tech responsables
\item \textbf{Entrepreneuriat green-tech} : Création d'entreprises à impact environnemental
\end{itemize}

\section{Conclusion Finale}

Ce projet de fin d'études illustre parfaitement comment l'innovation technologique peut servir des causes environnementales cruciales. En développant une solution complète de classification acoustique marine, nous avons démontré qu'il est possible de créer des outils à la fois techniquement excellents et écologiquement responsables.

Les résultats obtenus - une plateforme adoptée par la communauté internationale et des modèles d'IA performants - valident notre approche et ouvrent des perspectives prometteuses pour l'avenir de la gestion durable des ressources marines.

Au-delà des aspects techniques, ce travail souligne l'importance de la collaboration interdisciplinaire et de l'engagement des nouvelles générations d'ingénieurs dans la résolution des défis environnementaux. C'est avec cette conviction que nous abordons la suite de notre parcours professionnel, déterminés à continuer de mettre la technologie au service de la planète.

L'océan, qui couvre 71\% de notre planète et abrite une biodiversité exceptionnelle, mérite que nous mobilisions nos meilleures innovations pour le comprendre et le protéger. Ce stage nous a permis d'apporter notre pierre à cet édifice, et nous espérons que ce travail inspirera d'autres initiatives similaires.

Comme le disait Jacques Cousteau : \textit{"Il faut aller voir sous la mer pour comprendre notre monde"}. Grâce aux technologies modernes, nous pouvons désormais non seulement voir, mais aussi comprendre et protéger cet univers fascinant qu'est l'océan.

\section{Apprentissages Personnels}

Ce stage a été une expérience formatrice exceptionnelle, permettant d'acquérir des compétences techniques avancées tout en contribuant à un projet ayant un impact environnemental positif. La combinaison d'expertise en acoustique sous-marine, intelligence artificielle et développement web constitue un atout unique pour la suite de notre parcours professionnel.

\end{document}