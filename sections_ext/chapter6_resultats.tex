\section{Introduction}
Ce chapitre présente les résultats concrets et quantifiables obtenus à l'issue du développement de la plateforme. Nous y évaluons la performance sous trois angles : l'efficacité du modèle de classification d'espèces, la performance technique de l'infrastructure logicielle, et la valeur ajoutée de l'interface utilisateur. Ces résultats valident l'approche adoptée et démontrent la viabilité de la solution.

\section{Performance du Modèle de Classification}
Le cœur de notre solution réside dans sa capacité à identifier automatiquement les espèces marines. Le modèle a été entraîné pour classifier trois espèces pélagiques communes et commercialement importantes : l'anchois (\textit{Engraulis encrasicolus}), la sardine (\textit{Sardina pilchardus}) et le thon (\textit{Thunnus thynnus}).

\subsection{Jeu de Données et Pré-traitement}
\begin{itemize}
    \item \textbf{Source :} Données de la NOAA, corrélées avec des campagnes de chalutage pour obtenir des étiquettes de vérité-terrain.
    \item \textbf{Volume :} Environ 15,000 extraits d'échogrammes de 256x256 pixels, équitablement répartis entre les trois espèces et une classe "autre/bruit de fond".
    \item \textbf{Pré-traitement :} Chaque extrait est converti en un tenseur à 3 canaux, où chaque canal représente l'intensité du signal (valeur Sv) pour une fréquence différente (38, 120, 200 kHz). Une normalisation des données a été appliquée.
    \item \textbf{Split :} Le jeu de données a été divisé en 70\% pour l'entraînement, 15\% pour la validation et 15\% pour le test final.
\end{itemize}

\subsection{Métriques de Performance}
Le modèle final a été évalué sur l'ensemble de test. Les résultats sont synthétisés dans la matrice de confusion (Figure \ref{fig:confusion_matrix_3}).

\begin{figure}[H]
    \centering
    \begin{tikzpicture}
        \matrix[matrix of nodes,
                nodes={draw, minimum size=2cm, text width=2cm, align=center},
                row 1/.style={nodes={draw=none, fill=none}},
                column 1/.style={nodes={draw=none, fill=none}},
                column 2/.style={fill=green!20},
                column 3/.style={fill=green!20},
                column 4/.style={fill=green!20},
                ] {
                  & \textbf{Prédit: Anchois} & \textbf{Prédit: Sardine} & \textbf{Prédit: Thon} \\
        \textbf{Vrai: Anchois} & 94\% & 4\% & 2\% \\
        \textbf{Vrai: Sardine} & 5\% & 93\% & 2\% \\
        \textbf{Vrai: Thon} & 1\% & 3\% & 96\% \\
        };
    \end{tikzpicture}
    \caption{Matrice de confusion normalisée pour le modèle de classification.}
    \label{fig:confusion_matrix_3}
\end{figure}

Les métriques de classification globales sont :
\begin{table}[H]
    \centering
    \begin{tabular}{l c c c}
        \toprule
        \textbf{Espèce} & \textbf{Précision} & \textbf{Rappel} & \textbf{Score F1} \\
        \midrule
        Anchois & 0.94 & 0.94 & 0.94 \\
        Sardine & 0.93 & 0.93 & 0.93 \\
        Thon & 0.96 & 0.96 & 0.96 \\
        \midrule
        \textbf{Moyenne} & \textbf{0.94} & \textbf{0.94} & \textbf{0.94} \\
        \textbf{Accuracy} & \multicolumn{3}{c}{\textbf{92\%}} \\
        \bottomrule
    \end{tabular}
    \caption{Principales métriques de performance du modèle.}
    \label{tab:classification_metrics_3}
\end{table}

\section{Performance de la Plateforme Technique}
\subsection{Benchmarks de l'API Backend}
Tests de charge effectués avec \tech{k6}:
\begin{itemize}
    \item \textbf{Temps de réponse moyen} : < 50ms
    \item \textbf{Temps de réponse p95} : < 150ms
    \item \textbf{Requêtes par seconde soutenues} : > 500 req/s.
    \item \textbf{Génération d'un échogramme (50MB)} : $\sim$2.5 secondes.
\end{itemize}

\subsection{Performance du Frontend}
\begin{itemize}
    \item \textbf{Temps de chargement initial (LCP)} : < 2 secondes.
    \item \textbf{Rendu d'un échogramme interactif} : Navigation fluide à 60 FPS sur des échogrammes de plus de 5 millions de points de données.
\end{itemize}

\section{Visualisations et Interface Utilisateur}
\begin{figure}[H]
    \centering
    \includegraphics[width=\textwidth]{proteyus_applications.png}
    \caption{Exemple de visualisation : un échogramme multi-fréquences avec un banc de sardines identifié par le modèle.}
    \label{fig:ui_example_3}
\end{figure}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{proteyus_link.png}
    \caption{Résultat d'une recherche sur l'outil "NOAA Dataset Explorer".}
    \label{fig:explorer_result_3}
\end{figure}

\section{Conclusion}
Les résultats valident la pertinence de nos choix techniques et scientifiques. Le modèle de classification est performant, l'infrastructure est robuste et scalable, et les interfaces sont intuitives.