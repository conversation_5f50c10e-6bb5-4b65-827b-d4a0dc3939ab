\section{Introduction}
Ce chapitre est le cœur technique de ce rapport. Il détaille la "salle des machines" de notre projet, en expliquant la conception, l'architecture et l'implémentation de chaque composant majeur de la plateforme. Nous suivrons le parcours de la donnée, depuis sa source brute jusqu'à sa transformation en une information à valeur ajoutée.

\section{Développement Backend : Le Moteur de la Plateforme}
Le backend, développé avec \tech{FastAPI}, est responsable de toute la logique métier, du traitement des données et de la communication avec le frontend et les bases de données.

\subsection{L'API du "NOAA Dataset Explorer"}
Le premier service développé fut l'API permettant d'interroger le catalogue de la NOAA.
\begin{enumerate}
    \item \textbf{Service d'Indexation :} Un script \tech{Python} asynchrone tourne en tâche de fond pour scanner périodiquement le bucket S3 de la NOAA. Pour chaque fichier trouvé, il extrait les métadonnées (navire, date, instrument) à partir du chemin du fichier et stocke ces informations dans une base de données Redis, optimisée pour des recherches rapides.
    \item \textbf{Points de terminaison de l'API :} Des endpoints REST ont été créés pour :
    \begin{itemize}
        \item \code{GET /files}: Permet de rechercher et filtrer les fichiers par date, navire, etc. La requête interroge Redis pour une réponse quasi-instantanée.
        \item \code{POST /download-batch}: Prend une liste d'identifiants de fichiers et génère un fichier d'archive (.zip) à la volée, en téléchargeant en parallèle les fichiers depuis S3.
    \end{itemize}
\end{enumerate}

\subsection{Le Pipeline de Traitement Acoustique}
C'est le composant le plus complexe du backend.
\begin{figure}[H]
    \centering
    \begin{tikzpicture}[node distance=2cm, auto, scale=0.8, every node/.style={scale=0.8}]
        \node (upload) [rectangle, draw, fill=blue!10] {1. Fichier .raw};
        \node (parse) [rectangle, draw, fill=yellow!20, right of=upload, xshift=1.5cm] {2. Parsing (PyEcholab)};
        \node (calib) [rectangle, draw, fill=yellow!20, right of=parse, xshift=1.5cm] {3. Calibration (TVG)};
        \node (echo) [rectangle, draw, fill=green!10, right of=calib, xshift=2cm] {4. Image Échogramme};
        \node (classify) [rectangle, draw, fill=purple!20, below of=echo] {5. Inférence Modèle CNN};
        \draw[->] (upload) -- (parse);
        \draw[->] (parse) -- (calib);
        \draw[->] (calib) -- (echo);
        \draw[->] (echo) -- (classify);
    \end{tikzpicture}
    \caption{Flux de traitement d'un fichier acoustique dans le backend.}
    \label{fig:backend_pipeline_2}
\end{figure}
\begin{enumerate}
    \item \textbf{Parsing :} Le fichier binaire est lu et décodé grâce à notre version optimisée de \tech{PyEcholab}.
    \item \textbf{Calibration :} Des corrections radiométriques cruciales sont appliquées, comme la compensation de la perte de transmission (TVG - Time Varying Gain).
    \item \textbf{Génération de l'échogramme :} Les données calibrées (valeurs de Sv) sont converties en une image (matrice 2D).
    \item \textbf{Inférence (si demandée) :} L'image de l'échogramme est envoyée au service de Machine Learning.
\end{enumerate}

\section{Développement Frontend : L'Expérience Utilisateur}
Le frontend, développé avec \tech{React} et \tech{TypeScript}, a été conçu pour être à la fois puissant et intuitif.

\subsection{Composants du "Dataset Explorer"}
\begin{itemize}
    \item \textbf{Barre de Recherche et Filtres :} Des composants contrôlés permettent de définir des plages de dates, de sélectionner des navires, etc.
    \item \textbf{Table de Résultats Virtualisée :} Pour afficher des milliers de résultats sans ralentir le navigateur, nous avons utilisé une bibliothèque de "virtualisation" (\tech{TanStack Virtual}).
\end{itemize}

\subsection{Le Visualiseur d'Échogrammes Interactif}
\begin{itemize}
    \item \textbf{Rendu sur Canvas :} L'échogramme principal est dessiné sur un élément \code{<canvas>} HTML5.
    \item \textbf{Gestion du Zoom et du Panoramique :} La logique de navigation a été implémentée en capturant les événements de la souris.
    \item \textbf{Superposition des Résultats :} Les résultats de la classification (boîtes englobantes) sont dessinés sur un \code{<canvas>} transparent superposé.
\end{itemize}

\section{Implémentation du Machine Learning}
Le pipeline de Machine Learning a été développé avec \tech{TensorFlow} et \tech{Keras}.

\subsection{Préparation et Augmentation des Données}
\begin{enumerate}
    \item \textbf{Extraction des Patchs :} Un script a été développé pour extraire des "patchs" (imagettes) de 256x256 pixels centrés sur les bancs de poissons.
    \item \textbf{Création des Tenseurs Multi-canaux :} Pour chaque patch, les données des différentes fréquences (38, 120, 200 kHz) ont été combinées pour former une image à 3 canaux.
    \item \textbf{Augmentation de Données Spécifique au Domaine :}
    \begin{itemize}
        \item Ajout de bruit gaussien pour simuler le bruit ambiant.
        \item Décalages horizontaux et verticaux.
        \item Masquage de bandes de fréquences pour simuler des interférences.
    \end{itemize}
\end{enumerate}

\subsection{Entraînement et Suivi des Expériences}
\begin{itemize}
    \item \textbf{Architecture du Modèle :} Nous avons implémenté une architecture de type CNN inspirée de ResNet.
    \item \textbf{Boucle d'Entraînement :} L'entraînement a été effectué sur des GPU dans le cloud, avec des callbacks \tech{Keras} comme \code{ModelCheckpoint} et \code{EarlyStopping}.
    \item \textbf{Suivi avec TensorBoard :} Toutes les métriques et hyperparamètres ont été journalisés pour comparer les modèles.
\end{itemize}