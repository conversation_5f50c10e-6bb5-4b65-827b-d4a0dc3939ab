\section{Introduction}
Le succès d'un projet d'ingénierie logicielle repose sur des fondations technologiques solides et cohérentes. Les choix d'outils, de frameworks et de langages ne sont pas neutres ; ils conditionnent la performance, la scalabilité, la maintenabilité et la vitesse de développement du produit final. Ce chapitre présente l'écosystème technologique que nous avons sélectionné et assemblé pour construire notre plateforme de classification acoustique, en justifiant chaque décision au regard des exigences uniques de notre projet.

\section{Architecture Globale de la Solution}
Pour répondre aux défis de volume de données, de complexité de calcul et de besoin d'accessibilité, nous avons opté pour une architecture microservices moderne, entièrement conçue pour le cloud.

\begin{figure}[H]
    \centering
    \includegraphics[width=\textwidth]{tool_architecture.png}
    \caption{Architecture en microservices de la plateforme.}
    \label{fig:system_architecture_3}
\end{figure}

Cette architecture se décompose en quatre couches principales :
\begin{enumerate}
    \item \textbf{La couche Frontend (Client)} : L'interface utilisateur web, développée en \tech{React.js}, qui s'exécute dans le navigateur de l'utilisateur.
    \item \textbf{La couche API Gateway} : Un point d'entrée unique qui route les requêtes du client vers les services internes appropriés (Authentification, Rate Limiting).
    \item \textbf{La couche Backend (Microservices)} : Le cœur logique de l'application, où chaque service a une responsabilité unique.
    \item \textbf{La couche de Persistance (Données)} : Elle regroupe les différentes bases de données et systèmes de stockage (PostgreSQL, Redis, S3).
\end{enumerate}

\section{Technologies Backend : La Puissance de Python}
\subsection{FastAPI : Le Framework Web Asynchrone}
Plutôt que d'utiliser des frameworks plus anciens comme Django ou Flask, nous avons choisi \tech{FastAPI}.
\begin{itemize}
    \item \textbf{Performance Brute :} Grâce à son utilisation de l'asynchrone (\tech{asyncio}) et de \tech{Uvicorn}, \tech{FastAPI} est l'un des frameworks \tech{Python} les plus rapides.
    \item \textbf{Rapidité de Développement :} Il utilise le typage standard de \tech{Python} pour la validation automatique des données (via \tech{Pydantic}) et la génération de documentation interactive (Swagger UI).
\end{itemize}

\subsection{PyEcholab : Notre Boîte à Outils Acoustique}
Nous avons fait le choix stratégique de créer notre propre version optimisée de \tech{PyEcholab}.
\begin{itemize}
    \item Amélioration des performances de lecture des fichiers en utilisant des techniques de \textit{memory mapping}.
    \item Ajout du support complet pour le format \tech{EK80}.
    \item Intégration avec \tech{Numba} (un compilateur JIT) pour accélérer les calculs de traitement du signal.
\end{itemize}

\section{Technologies Frontend : L'Interactivité de React}
Pour l'interface utilisateur, notre choix s'est porté sur \tech{React.js} avec \tech{TypeScript}.
\begin{itemize}
    \item \textbf{Architecture par Composants :} Permet de construire des interfaces complexes et réutilisables.
    \item \textbf{Haute Performance :} Son DOM virtuel permet des mises à jour efficaces, crucial pour la manipulation de visualisations de données complexes.
    \item \textbf{Typage avec TypeScript :} Ajoute un typage statique au \tech{JavaScript}, rendant notre code plus robuste et lisible.
\end{itemize}
Pour les visualisations d'échogrammes, nous avons combiné \tech{D3.js} pour les axes et les échelles de couleurs, et l'API \tech{Canvas} de HTML5, avec \tech{WebGL} pour les visualisations les plus denses.

\section{Intelligence Artificielle : L'Écosystème TensorFlow}
\tech{TensorFlow}, développé par Google, a été choisi comme framework de deep learning.
\begin{itemize}
    \item \textbf{Flexibilité et Puissance :} L'API de haut niveau \tech{Keras} permet de prototyper rapidement des architectures de réseaux de neurones.
    \item \textbf{Outils de Production :} \tech{TensorFlow Serving} et \tech{TensorFlow Lite} offrent un écosystème complet pour le déploiement.
\end{itemize}
Nous avons également utilisé \tech{Scikit-learn} pour le pré-traitement des données et l'évaluation des métriques.

\section{Infrastructure et Déploiement}
\begin{itemize}
    \item \textbf{Conteneurisation avec Docker :} Chaque microservice est encapsulé dans un conteneur pour garantir un environnement d'exécution cohérent et reproductible.
    \item \textbf{Déploiement avec Fly.io :} Un PaaS (Platform as a Service) moderne qui simplifie le déploiement d'applications conteneurisées à l'échelle mondiale.
    \item \textbf{Gestion de Version avec GitHub :} Pour le versionnage, l'intégration continue (\tech{GitHub Actions}) et la revue de code.
\end{itemize}

\section{Conclusion}
La stack technologique a été choisie pour un équilibre entre productivité, performance et scalabilité. En combinant le meilleur du web, de la science des données et du cloud, nous avons pu construire une solution robuste.