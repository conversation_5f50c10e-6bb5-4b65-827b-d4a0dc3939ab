\section{Introduction : Plongée en Eaux Profondes}
Avant de pouvoir concevoir une solution logicielle pertinente, il est impératif de comprendre le monde dans lequel elle s'inscrit. Ce chapitre est une immersion dans le domaine hautement spécialisé de l'acoustique halieutique. Nous y détaillerons la démarche d'apprentissage autonome et de recherche documentaire qui nous a permis de passer de connaissances d'ingénieur informaticien généraliste à une compréhension appliquée de la physique de la propagation du son dans l'eau, de la technologie complexe des échosondeurs et des défis quotidiens des scientifiques et gestionnaires des pêches. Cette compréhension a été le socle sur lequel nous avons bâti nos spécifications techniques et fonctionnelles.

\section{Principes Fondamentaux de l'Acoustique Sous-Marine}
\subsection{Le Son, Média de Prédilection pour l'Exploration Océanique}
L'eau de mer est un milieu quasiment opaque aux ondes électromagnétiques. La lumière visible est absorbée et diffusée en quelques centaines de mètres tout au plus, tandis que les ondes radio et radar sont réfléchies par la surface. Le son, en revanche, s'y propage avec une efficacité remarquable, ce qui en fait le seul outil viable pour "voir" à travers la colonne d'eau sur de grandes distances.

\subsubsection{Célérité du Son et Facteurs d'Influence}
La vitesse de propagation du son dans l'eau, ou célérité ($c$), est typiquement d'environ 1500 m/s. Elle n'est cependant pas constante et sa modélisation précise est essentielle pour une localisation exacte des cibles. Plusieurs équations empiriques, comme celles de Mackenzie (1981) ou de Chen et Millero (1977), permettent de la calculer en fonction de trois paramètres environnementaux majeurs :
\begin{itemize}
    \item \textbf{Température (T)} : C'est le facteur le plus influent. Une augmentation de 1°C augmente la célérité d'environ 4 m/s.
    \item \textbf{Salinité (S)} : Une augmentation de 1 PSU (Practical Salinity Unit) augmente la célérité d'environ 1.4 m/s.
    \item \textbf{Pression (P) ou Profondeur (Z)} : Une augmentation de 100 mètres de profondeur augmente la célérité d'environ 1.7 m/s.
\end{itemize}
Ces variations créent des gradients de célérité verticaux qui peuvent courber les rayons sonores, un phénomène appelé réfraction, qui doit être pris en compte dans les calculs de localisation.
\subsubsection{Atténuation et Absorption}
L'énergie d'une onde sonore ne se propage pas indéfiniment. Elle subit une atténuation (Transmission Loss, TL) qui a deux composantes principales :
\begin{itemize}
    \item \textbf{L'atténuation géométrique (Divergence sphérique)} : L'énergie de l'onde se répartit sur une surface de plus en plus grande. Pour une source ponctuelle, cette perte est proportionnelle à $20 \log_{10}(R)$, où $R$ est la distance.
    \item \textbf{L'absorption chimique ($\alpha$)} : L'énergie acoustique est convertie en chaleur par des processus de relaxation moléculaire (notamment avec l'acide borique et le sulfate de magnésium dans l'eau de mer). Cette absorption est fortement dépendante de la fréquence : les hautes fréquences sont absorbées beaucoup plus rapidement que les basses fréquences. C'est pourquoi les sonars longue portée utilisent de basses fréquences. Cette perte est proportionnelle à $\alpha R$.
\end{itemize}
La formule complète de la perte de transmission est donc : $TL = 20 \log_{10}(R) + \alpha R$. Notre pipeline de traitement doit calculer $\alpha$ et compenser cette perte pour chaque échantillon.

\subsection{L'Équation du SONAR Active}
L'équation du sonar est le bilan de puissance qui régit toute détection acoustique active. Elle relie l'énergie émise à l'énergie reçue. Dans sa forme la plus simple, le niveau d'écho (EL, en dB) mesuré par le récepteur est :
\begin{equation}
EL = SL - 2TL + TS
\end{equation}
Chaque terme représente un maillon de la chaîne acoustique :
\begin{itemize}
    \item \textbf{SL (Source Level)} : La "puissance" de l'émission, une caractéristique intrinsèque de l'échosondeur, spécifiée par le manufacturier.
    \item \textbf{TL (Transmission Loss)} : L'atténuation sur le trajet aller (source -> cible) et retour (cible -> source), d'où le facteur 2.
    \item \textbf{TS (Target Strength)} : L'indice de réflexion de la cible. C'est la mesure de la capacité de la cible à renvoyer l'énergie acoustique vers l'émetteur. \textbf{C'est la variable d'intérêt primordiale pour l'identification et la quantification.}
\end{itemize}
Pour des cibles diffuses comme des bancs de poissons, on parle de Rétrodiffusion Volumique (Volume Backscattering Strength, Sv), qui est l'intégrale des TS de tous les diffuseurs dans un volume d'eau unitaire. C'est la valeur de Sv qui est cartographiée dans un échogramme.
\subsection{La Signature Acoustique des Cibles Biologiques}
\subsubsection{La Vessie Natatoire : Le Réflecteur Dominant}
La plupart des poissons osseux possèdent une vessie natatoire, une poche interne remplie de gaz qui leur permet de contrôler leur flottabilité. D'un point de vue acoustique, cette poche de gaz constitue un réflecteur extraordinairement efficace. Le contraste d'impédance acoustique entre le gaz et l'eau est très élevé, renvoyant une grande partie de l'énergie sonore. La TS d'un poisson est donc majoritairement (souvent >95\%) due à sa vessie natatoire. La taille, la forme, l'orientation et la composition du gaz de cette vessie sont des caractéristiques spécifiques à l'espèce, au stade de vie et au comportement, ce qui en fait la source principale d'information pour la classification.

\subsubsection{Réponse Fréquentielle et Résonance}
La vessie natatoire se comporte comme un oscillateur. Elle possède une fréquence de résonance qui dépend de sa taille et de la pression ambiante. Lorsqu'un échosondeur émet à une fréquence proche de cette résonance, la TS du poisson augmente de manière spectaculaire. Les échosondeurs modernes à large bande (broadband ou chirp), comme le Simrad EK80, sont capables de balayer une gamme de fréquences, permettant de mesurer la réponse acoustique complète de la cible. C'est la forme de cette courbe de réponse fréquentielle qui est une signature quasi-unique de l'espèce (Figure \ref{fig:freq_response_detail_4}).

\begin{figure}[H]
    \centering
    % Placeholder for a detailed frequency response graph
    \begin{tikzpicture}
        \draw[->] (0,0) -- (8,0) node[right] {Fréquence (kHz)};
        \draw[->] (0,0) -- (0,5) node[above] {Target Strength (dB)};
        \draw[thick, blue] (1,1) .. controls (2,4) and (3,3) .. (7,2) node[right] {Espèce A (ex: Hareng)};
        \draw[thick, red] (1,2) .. controls (3,2.5) and (5,1) .. (7,1.5) node[right] {Espèce B (ex: Maquereau)};
        \draw[dashed, gray] (2.2,0) -- (2.2,4);
        \node[below] at (2.2,0) {$f_{res, A}$};
    \end{tikzpicture}
    \caption{Illustration schématique de la réponse en fréquence (TS vs fréquence) pour deux espèces différentes.}
    \label{fig:freq_response_detail_4}
\end{figure}
\section{Technologie des Échosondeurs Scientifiques}
\subsection{Le Système Simrad EK80 : État de l'Art}
Notre projet s'est principalement focalisé sur les données issues des systèmes Simrad, et en particulier de l'EK80, qui représente la dernière génération.

\begin{table}[H]
    \centering
    \begin{tabularx}{\textwidth}{l|X|X}
        \toprule
        \textbf{Caractéristique} & \textbf{Simrad EK60 (Ancienne génération)} & \textbf{Simrad EK80 (Nouvelle génération)} \\
        \midrule
        \textbf{Signal} & Impulsion à fréquence unique (CW - Continuous Wave). & Impulsion à balayage de fréquence (FM/Chirp) ou CW. \\
        \textbf{Résolution} & Limitée par la durée de l'impulsion. & La compression d'impulsion sur le signal Chirp permet d'obtenir une haute résolution et une grande énergie. \\
        \textbf{Information} & Fournit une valeur de Sv par fréquence. & Fournit une courbe de réponse fréquentielle continue pour chaque cible. \\
        \textbf{Calibration} & Calibration sur sphère à chaque fréquence. & Calibration complexe sur toute la bande de fréquence. \\
        \textbf{Format de donnée} & \file{.raw} (versions antérieures) & \file{.raw} (version 3), contenant les données brutes complexes. \\
        \bottomrule
    \end{tabularx}
    \caption{Comparaison technologique entre les générations d'échosondeurs Simrad EK60 et EK80.}
    \label{tab:ek_comparison_4}
\end{table}

\subsection{L'Anatomie Complexe du Fichier \file{.raw}}
Le format \file{.raw} est le conteneur de données qui a constitué notre principal défi en matière de traitement. Un fichier \file{.raw} est une séquence de "datagrammes", chacun étant un paquet de données autosuffisant.

\begin{figure}[H]
    \centering
    \begin{tikzpicture}
        \node[draw, rectangle, fill=gray!20, text width=12cm, align=left] (file) {
            \textbf{Fichier.raw}\\
            [Datagram Length] [Datagram Type: CON0] [Timestamp] [Config Data] [Datagram Length]\\
            [Datagram Length] [Datagram Type: NME0] [Timestamp] [GPS String] [Datagram Length]\\
            [Datagram Length] [Datagram Type: RAW3] [Timestamp] [Ping Data Chan 1] ... [Datagram Length]\\
            ...
        };
    \end{tikzpicture}
    \caption{Représentation conceptuelle de la structure d'un fichier \file{.raw}.}
    \label{fig:raw_datagrams_4}
\end{figure}

Notre parseur doit lire la longueur du prochain datagramme, lire ce bloc de données, l'interpréter en fonction de son type, puis passer au suivant. C'est une tâche séquentielle qui est difficile à paralléliser.
\section{Analyse des Besoins Métier et du Workflow}
\subsection{Cartographie des Acteurs}
Plusieurs personas d'utilisateurs ont été identifiés :
\begin{itemize}
    \item \textbf{L'Acousticien Chercheur :} A besoin d'un contrôle total sur le traitement, d'algorithmes transparents, et de la capacité à exporter les données.
    \item \textbf{Le Biologiste des Pêches :} A besoin d'estimations de biomasse et de distributions d'espèces pour des rapports. Il privilégie une interface visuelle et des résultats agrégés.
    \item \textbf{Le Gestionnaire de Pêcheries (Gouvernemental) :} A besoin de tableaux de bord synthétiques pour surveiller les stocks et vérifier la conformité aux quotas.
    \item \textbf{L'Armateur / Capitaine de Pêche :} A besoin d'une aide à la décision en temps réel simple et robuste.
\end{itemize}
Notre projet initial s'est concentré sur les besoins des deux premiers personas.

\subsection{Le Goulot d'Étranglement Humain}
Le flux de travail actuel est un long processus séquentiel :
\begin{enumerate}
    \item \textbf{Post-campagne :} Rapatriment des disques durs.
    \item \textbf{Pré-traitement :} Organisation et calibration des fichiers (jours/semaines).
    \item \textbf{Analyse ("Scrutinizing") :} L'expert acousticien visualise les échogrammes un par un et assigne une espèce manuellement (semaines/mois). C'est une tâche chronophage et subjective.
    \item \textbf{Calcul de biomasse :} Le logiciel intègre les valeurs de Sv pour estimer une densité.
    \item \textbf{Rapport :} Les résultats sont intégrés dans un rapport, souvent 6 à 12 mois après la collecte.
\end{enumerate}

\section{Conclusion : Définition d'une Vision Produit}
Cette analyse a permis de définir la vision de notre produit. Il devait être :
\begin{itemize}
    \item \textbf{Intégrée :} Couvrir tout le spectre, de l'accès à la donnée brute jusqu'au résultat.
    \item \textbf{Automatisée :} Réduire drastiquement les tâches manuelles répétitives.
    \item \textbf{Intelligente :} Assister l'expert en proposant des classifications, tout en lui laissant la possibilité de valider.
    \item \textbf{Accessible :} Rendre la consultation et l'analyse possibles via un simple navigateur web.
    \item \textbf{Scalable :} Être capable de traiter des volumes de données croissants.
\end{itemize}
C'est sur la base de cette vision que nous avons pu entamer l'étude de l'existant et la conception de notre architecture.