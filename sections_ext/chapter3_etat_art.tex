\section{Introduction}
L'innovation disruptive naît rarement d'un vide. Elle émerge d'une analyse fine de l'existant, de la compréhension de ses forces et de l'identification de ses lacunes. Ce chapitre propose une revue critique et approfondie du paysage technologique actuel inhérent à l'analyse acoustique marine. Nous y examinerons les solutions commerciales établies, les bibliothèques open-source, et les sources de données publiques. Cette analyse mettra en évidence le caractère novateur de notre projet et justifiera la nécessité de développer une solution entièrement nouvelle.

\section{Analyse des Logiciels Commerciaux}
Le marché est dominé par un acteur principal, qui définit de facto les standards de l'industrie.

\subsection{Echoview : La Référence Incontournable}
\tech{Echoview}, développé par la société australienne Echoview Software, est le leader mondial des logiciels de traitement de données d'hydroacoustique. C'est un outil de bureau extrêmement puissant et complet.

\begin{itemize}
    \item \textbf{Forces :}
    \begin{itemize}
        \item \textbf{Exhaustivité :} Supporte une vaste gamme de formats d'échosondeurs et offre des centaines d'opérateurs de traitement du signal pour la calibration, le filtrage, la détection de bancs, etc.
        \item \textbf{Maturité :} Plus de 20 ans de développement, une documentation très fournie et une large communauté d'utilisateurs scientifiques.
        \item \textbf{Support :} Support technique réactif et mises à jour régulières.
    \end{itemize}
    \item \textbf{Faiblesses Fondamentales :}
    \begin{itemize}
        \item \textbf{Architecture Monolithique :} C'est un logiciel de bureau pour Windows uniquement. Il n'est pas conçu pour le cloud, le traitement distribué ou l'accès à distance. La scalabilité est quasi inexistante.
        \item \textbf{Coût Prohibitif :} Le modèle de licence est très onéreux (plusieurs milliers à dizaines de milliers d'euros par poste et par an), ce qui le rend inaccessible à de nombreux laboratoires, PME ou pays en développement.
        \item \textbf{Absence d'API Moderne :} L'automatisation se fait via une ancienne interface COM, complexe à utiliser et limitée à l'écosystème Windows. Il n'y a pas d'API REST pour une intégration web.
        \item \textbf{Pas d'Intelligence Artificielle :} \tech{Echoview} est un outil de traitement du signal, pas une plateforme d'IA. La classification d'espèces reste un processus entièrement manuel ou basé sur des règles simples.
    \end{itemize}
\end{itemize}
\textbf{Conclusion :} \tech{Echoview} est un excellent outil pour l'analyse approfondie par un expert, mais il est fondamentalement inadapté à la création d'une plateforme web, scalable, automatisée et collaborative comme celle que nous visions.

\section{Évaluation des Bibliothèques Open-Source}
L'écosystème open-source offre des briques logicielles intéressantes, mais aucune solution complète.

\subsection{PyEcholab : La Fondation Historique}
Développée par le \tech{NOAA Alaska Fisheries Science Center}, \tech{PyEcholab} est une bibliothèque \tech{Python} pour lire et effectuer des traitements de base sur les fichiers \file{.raw}.
\begin{itemize}
    \item \textbf{Forces :}
        \begin{itemize}
            \item Pionnière dans le domaine, elle a posé les bases de la lecture des formats Simrad en \tech{Python}.
            \item Relativement simple d'utilisation pour des tâches basiques.
        \end{itemize}
    \item \textbf{Faiblesses :}
        \begin{itemize}
            \item \textbf{Projet en dormance :} Le développement est très peu actif depuis plusieurs années.
            \item \textbf{Performances limitées :} Le code est écrit en pur \tech{Python} et n'est pas optimisé pour la vitesse ou la gestion de la mémoire, le rendant très lent sur de gros fichiers.
            \item \textbf{Support partiel :} Ne supporte pas entièrement les fonctionnalités avancées des formats récents (EK80 broadband).
        \end{itemize}
\end{itemize}

\subsection{Echopype : La Modernisation}
\tech{Echopype} est un projet plus récent (maintenu par l'Université de Washington) qui vise à moderniser l'écosystème. Il s'appuie sur des standards comme \tech{xarray} et \tech{netCDF} pour créer un format de données acoustiques unifié.
\begin{itemize}
    \item \textbf{Forces :}
        \begin{itemize}
            \item \textbf{Approche moderne :} Standardise les données dans un format \tech{netCDF} bien défini, ce qui facilite l'interopérabilité.
            \item \textbf{Communauté active :} Le projet est en développement actif et bénéficie d'une communauté grandissante.
        \end{itemize}
    \item \textbf{Faiblesses :}
        \begin{itemize}
            \item \textbf{Focalisé sur la standardisation :} Son objectif principal est la conversion de format, pas le traitement avancé ou la classification.
            \item \textbf{Pas d'interface utilisateur :} C'est une bibliothèque pour développeurs, pas un outil final pour les utilisateurs.
            \item \textbf{Pas de solution de déploiement :} N'offre aucune brique pour une utilisation via une API web ou dans le cloud.
        \end{itemize}
\end{itemize}

\subsection{Synthèse Comparative}
Le tableau \ref{tab:solutions_comparison_3} résume la position de notre solution par rapport à l'existant.

\begin{table}[H]
    \centering
    \small
    \begin{tabularx}{\textwidth}{l|c|c|c|X}
        \toprule
        \textbf{Critère} & \textbf{Echoview} & \textbf{PyEcholab} & \textbf{Echopype} & \textbf{Notre Solution (Proteyus)} \\
        \midrule
        \textbf{Type} & Logiciel Bureau & Bibliothèque & Bibliothèque & Plateforme Web Intégrée \\
        \textbf{Cible} & Expert Acoustique & Développeur & Développeur & Chercheur, Gestionnaire, Opérationnel \\
        \textbf{Coût} & €€€€€ & Gratuit & Gratuit & Modèle SaaS \\
        \textbf{Accès Web} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{green!50!black}{\sffamily\checkmark} \\
        \textbf{API REST} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{green!50!black}{\sffamily\checkmark} \\
        \textbf{Scalabilité Cloud} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{green!50!black}{\sffamily\checkmark} \\
        \textbf{Classification IA} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{green!50!black}{\sffamily\checkmark} \\
        \textbf{Pipeline intégré} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{red}{\sffamily X} & \textcolor{green!50!black}{\sffamily\checkmark} \\
        \bottomrule
    \end{tabularx}
    \caption{Matrice comparative des solutions existantes.}
    \label{tab:solutions_comparison_3}
\end{table}

\section{Le Défi du Jeu de Données : Le Cas NOAA}
L'un des plus grands obstacles au développement de modèles d'IA en acoustique marine est l'accès aux données. Le \tech{National Oceanic and Atmospheric Administration} (NOAA) des États-Unis maintient un immense catalogue public de données acoustiques brutes, une ressource inestimable mais techniquement difficile à exploiter.

\subsection{Problématiques du Dépôt NOAA}
Le jeu de données est hébergé sur un \textit{bucket} Amazon S3, accessible via une simple interface web statique.
\begin{itemize}
    \item \textbf{Navigation manuelle :} L'interface ne propose qu'une arborescence de dossiers.
    \item \textbf{Absence de recherche et de filtrage :} Il est impossible de rechercher des fichiers par date, par heure, par zone géographique ou par navire.
    \item \textbf{Pas de téléchargement en masse :} Il n'existe aucun mécanisme pour télécharger une série de fichiers.
    \item \textbf{Manque de métadonnées consolidées :} Les métadonnées sont implicites dans la structure des noms de dossiers et de fichiers.
    \item \textbf{Redondance et corruption :} Notre analyse exploratoire a révélé un taux significatif de fichiers dupliqués ou corrompus.
\end{itemize}

\subsection{Notre Solution : NOAA Dataset Explorer}
Conscients que ce verrou d'accès aux données était un frein majeur pour la communauté scientifique, nous avons développé une solution dédiée: \url{https://noaadataset.netlify.app/}.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.9\textwidth]{tool_interface.png}
    \caption{Interface de notre outil "NOAA Dataset Explorer".}
    \label{fig:noaa_explorer_ui_3}
\end{figure}

Il transforme le dépôt statique de la NOAA en une base de données dynamique et interrogeable, fournissant le "carburant" indispensable à l'entraînement de nos modèles d'IA.

\section{Conclusion : Une Opportunité d'Innovation Claire}
Cette étude de l'existant dresse un constat sans appel : \textbf{il n'existe aucune solution sur le marché qui réponde de manière intégrée aux besoins d'une analyse acoustique moderne}. Le paysage est fragmenté entre des logiciels de bureau dépassés dans leur architecture, et des briques open-source utiles mais incomplètes.

C'est précisément cette lacune que notre projet vise à combler. En nous appuyant sur les fondations de l'open-source, en résolvant le problème critique d'accès aux données et en intégrant les dernières avancées en matière d'IA et de technologies web, nous proposons une solution de rupture.