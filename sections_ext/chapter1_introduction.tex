\section{Contexte : L'Océan au Cœur des Enjeux du XXIe Siècle}
L'océan, souvent décrit comme la dernière frontière de notre planète, est un système complexe et vital qui couvre plus de 71\% de la surface terrestre. Il est le principal régulateur du climat mondial, absorbant plus de 90\% de la chaleur excédentaire due aux activités humaines et près de 30\% de nos émissions de dioxyde de carbone. Source de nourriture pour plus de trois milliards de personnes et fondement de vastes économies bleues, la santé de l'océan est indissociable de la survie et du bien-être de l'humanité.

Cependant, ce pilier de notre biosphère est aujourd'hui confronté à des menaces sans précédent. La surpêche, la pêche illégale, non déclarée et non réglementée (INN), la pollution plastique, l'acidification et le réchauffement des eaux exercent une pression anthropique immense sur les écosystèmes marins. L'Organisation des Nations Unies pour l'alimentation et l'agriculture (FAO) estime qu'en 2017, plus d'un tiers des stocks de poissons mondiaux étaient exploités à un niveau biologiquement non durable.

Face à cette urgence, la communauté internationale, à travers les Objectifs de Développement Durable (notamment l'ODD 14 : "Vie aquatique"), s'est engagée à conserver et à exploiter de manière durable les océans, les mers et les ressources marines. Atteindre cet objectif ambitieux ne peut se faire sans une connaissance précise, actualisée et spatialement explicite de l'état des écosystèmes marins. Il est impératif de passer d'une gestion réactive à une gestion prédictive et adaptative, ce qui requiert des outils de surveillance (monitoring) à haute résolution, capables de fournir des informations fiables en temps quasi-réel.

\section{La Révolution de l'Acoustique Active}
Dans cet effort de surveillance, l'acoustique sous-marine active s'est imposée depuis plusieurs décennies comme la technique de télédétection la plus puissante pour sonder la colonne d'eau. Contrairement à la lumière qui ne pénètre que les premières dizaines de mètres, les ondes sonores peuvent parcourir des kilomètres, offrant une fenêtre unique sur la vie pélagique.

Les échosondeurs scientifiques, tels que les systèmes \tech{Simrad EK60} et, plus récemment, \tech{EK80} de Kongsberg Maritime, sont les instruments de prédilection pour les campagnes océanographiques d'évaluation des stocks halieutiques. En émettant une impulsion acoustique et en analysant l'écho rétrodiffusé, ils permettent de cartographier la biomasse, d'étudier la structure des bancs de poissons et d'analyser leur comportement.

Cependant, cette technologie, bien que mature, fait face à un nouveau défi : le "déluge de données" (data deluge). Un seul navire de recherche moderne peut générer plusieurs téraoctets de données acoustiques brutes (\file{.raw}) au cours d'une unique campagne. Ces données recèlent une richesse d'informations sans précédent, mais leur volume et leur complexité dépassent largement les capacités des méthodes d'analyse traditionnelles. Le traitement manuel ou semi-manuel de ces données est devenu le principal goulot d'étranglement qui freine la recherche et la prise de décision.

\section{Problématique Générale et Verrous Technologiques}
Le projet, mené au sein de la startup \textbf{Proteyus}, s'attaque directement à cette problématique centrale, qui peut être formulée ainsi : \textit{Comment transformer le paradigme actuel d'analyse acoustique, en passant d'un processus artisanal et laborieux à un pipeline industriel, automatisé et intelligent, capable de convertir des données brutes massives en informations actionnables pour une gestion durable des océans ?}

Cette problématique se décline en une série de verrous scientifiques, technologiques et opérationnels que notre projet vise à lever :
\begin{enumerate}
    \item \textbf{Le Verrou de l'Accès aux Données :} Les plus grands catalogues publics de données acoustiques, comme celui de la \tech{NOAA}, sont des ressources inestimables mais prisonnières d'interfaces statiques et rudimentaires. L'absence de moteur de recherche, de filtrage avancé ou de capacités de téléchargement par lots rend leur exploitation fastidieuse, voire prohibitive, pour l'entraînement de modèles d'IA à grande échelle.
    
    \item \textbf{Le Verrou du Traitement du Signal :} Le format de données \file{.raw} des échosondeurs Simrad est un standard de facto, mais il est propriétaire et binaire. Sa manipulation requiert une expertise pointue et des outils spécifiques. Il n'existe pas de solution "clé en main" pour intégrer facilement le décodage et la calibration de ces fichiers dans des applications web modernes.
    
    \item \textbf{Le Verrou de la Classification d'Espèces :} L'identification des espèces à partir de leur signature acoustique ("couleur acoustique") est le Saint Graal de l'acoustique halieutique. Bien que les experts humains puissent le faire avec une certaine précision, le processus est subjectif, lent et non scalable. L'automatisation de cette tâche via l'intelligence artificielle est une promesse de longue date, mais elle s'est heurtée à la complexité des signaux et au manque de données d'entraînement proprement étiquetées.
    
    \item \textbf{Le Verrou de l'Intégration et du Déploiement :} Le paysage technologique actuel est fragmenté. D'un côté, des logiciels de bureau monolithiques et coûteux comme \tech{Echoview} ; de l'autre, des bibliothèques open-source spécialisées mais isolées comme \tech{PyEcholab} ou \tech{Echopype}. Il manque une plateforme intégrée qui orchestre l'ensemble du pipeline, de la donnée brute à la visualisation interactive, dans une architecture cloud-native, scalable et accessible via un simple navigateur web.
\end{enumerate}

\section{Objectifs Spécifiques et Ambition du Projet}
Dans ce contexte, notre stage de fin d'études s'est vu confier des objectifs ambitieux, visant à apporter une solution concrète à chacun de ces verrous.

\subsection{Objectifs Techniques et d'Ingénierie}
\begin{itemize}
    \item \textbf{Créer une porte d'entrée vers les données massives :} Concevoir et développer une application web (\url{https://noaadataset.netlify.app/}) servant d'explorateur intelligent pour le catalogue NOAA, avec des fonctionnalités de recherche, de filtrage et de téléchargement par lots.
    \item \textbf{Bâtir un moteur de traitement acoustique as-a-service :} Implémenter une API backend robuste (\tech{FastAPI}) capable de recevoir des fichiers \file{.raw}, de les traiter à la volée (décodage, calibration, génération d'échogrammes) et de retourner des résultats exploitables.
    \item \textbf{Développer un "œil artificiel" pour la classification :} Concevoir, entraîner et évaluer des modèles de réseaux de neurones convolutionnels (CNN) pour la classification automatique d'espèces à partir des images d'échogrammes multi-fréquences.
    \item \textbf{Construire une interface homme-machine intuitive :} Développer un frontend réactif (\tech{React.js}) permettant une visualisation fluide et interactive des échogrammes, et une restitution claire des résultats de la classification.
    \item \textbf{Mettre en place une infrastructure de production :} Déployer l'ensemble de la solution sur une plateforme cloud moderne (\tech{Fly.io}) en utilisant les meilleures pratiques de l'ingénierie logicielle (conteneurisation avec \tech{Docker}, intégration et déploiement continus avec \tech{GitHub Actions}).
\end{itemize}

\subsection{Objectifs Scientifiques et de Recherche}
\begin{itemize}
    \item Valider de manière quantitative la performance des architectures CNN sur la tâche de classification acoustique, comparée aux méthodes traditionnelles.
    \item Étudier l'impact de l'utilisation de données multi-fréquences comme "canaux" d'une image d'entrée pour les modèles de deep learning.
    \item Contribuer à la méthodologie de constitution de jeux de données acoustiques étiquetés, en proposant un pipeline reproductible.
\end{itemize}

\section{Structure du Rapport}
Pour rendre compte de la richesse et de la complexité de ce projet, ce rapport est structuré de manière à suivre un cheminement logique, de la théorie à la pratique, et des résultats à leur interprétation.
\begin{itemize}
    \item \textbf{Chapitre 2 - Étude du Besoin et Contexte Scientifique :} Plonge dans les fondements théoriques de l'acoustique sous-marine et analyse en profondeur les processus métier et les besoins non comblés des utilisateurs finaux.
    \item \textbf{Chapitre 3 - État de l'Art et Positionnement du Projet :} Dresse un panorama critique des solutions existantes et positionne clairement notre projet comme une innovation de rupture.
    \item \textbf{Chapitre 4 - Architecture et Choix Technologiques :} Justifie en détail les décisions architecturales et les choix de la stack technologique qui sous-tendent la solution.
    \item \textbf{Chapitre 5 - Conception et Développement de la Solution :} Constitue le cœur du rapport, en détaillant l'implémentation de chaque brique logicielle : backend, frontend et pipeline de machine learning.
    \item \textbf{Chapitre 6 - Protocole Expérimental et Résultats :} Présente de manière rigoureuse la méthodologie d'évaluation et les performances quantitatives de la plateforme et des modèles d'IA.
    \item \textbf{Chapitre 7 - Analyse Critique et Discussion :} Propose une interprétation approfondie des résultats, analyse l'impact scientifique et sociétal du projet, et discute de ses limites.
    \item \textbf{Chapitre 8 - Conclusion Générale et Perspectives :} Synthétise les contributions du projet, dresse un bilan des apprentissages et ouvre des pistes pour les futurs travaux de recherche et de développement.
\end{itemize}