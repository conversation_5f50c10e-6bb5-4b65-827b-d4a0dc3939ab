% ===== FINAL COMPLETION OF THE REPORT =====

.error(f"Error processing {file_path}: {e}")
                
        # Conversion et normalisation
        X = np.array(X)
        y = self.label_encoder.fit_transform(y)
        
        # Normalisation des features
        X = self.scaler.fit_transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)
        
        # Split train/val/test
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=0.2, stratify=y, random_state=42
        )
        
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=0.25, stratify=y_temp, random_state=42
        )
        
        return {
            'train': (X_train, y_train),
            'val': (X_val, y_val),
            'test': (X_test, y_test),
            'label_encoder': self.label_encoder,
            'scaler': self.scaler
        }
    
    def extract_features(self, file_path: str) -> np.ndarray:
        """Extraction de features acoustiques"""
        # Lecture et calibration
        parser = RawFileParser(file_path)
        data = parser.parse()
        sv_data = self.calibrate(data)
        
        # Génération du spectrogramme
        spectrogram = self.compute_spectrogram(sv_data)
        
        # Redimensionnement standard
        spectrogram_resized = cv2.resize(
            spectrogram, 
            (self.config.input_width, self.config.input_height)
        )
        
        return spectrogram_resized
    
    def augment_data(self, spectrogram: np.ndarray) -> List[np.ndarray]:
        """Augmentation des données avec transformations acoustiques"""
        augmented = [spectrogram]
        
        # Ajout de bruit
        noise_levels = [0.01, 0.02, 0.05]
        for noise in noise_levels:
            noisy = spectrogram + np.random.normal(0, noise, spectrogram.shape)
            augmented.append(noisy)
        
        # Décalage temporel
        for shift in [-5, 5]:
            shifted = np.roll(spectrogram, shift, axis=1)
            augmented.append(shifted)
        
        # Masquage fréquentiel
        for _ in range(2):
            masked = spectrogram.copy()
            f_start = np.random.randint(0, spectrogram.shape[0] - 10)
            masked[f_start:f_start+10, :] *= 0.5
            augmented.append(masked)
        
        return augmented
\end{lstlisting}

\subsection{Architecture du Modèle CNN}

\subsubsection{Modèle Personnalisé pour Spectrogrammes Acoustiques}

\begin{lstlisting}[language=Python, caption=Architecture CNN spécialisée]
class AcousticCNN(tf.keras.Model):
    def __init__(self, num_classes=3, dropout_rate=0.5):
        super().__init__()
        
        # Bloc d'extraction de features bas niveau
        self.conv1_1 = Conv2D(32, (3, 3), padding='same', activation='relu')
        self.conv1_2 = Conv2D(32, (3, 3), padding='same', activation='relu')
        self.bn1 = BatchNormalization()
        self.pool1 = MaxPooling2D((2, 2))
        
        # Bloc de features moyennes
        self.conv2_1 = Conv2D(64, (3, 3), padding='same', activation='relu')
        self.conv2_2 = Conv2D(64, (3, 3), padding='same', activation='relu')
        self.bn2 = BatchNormalization()
        self.pool2 = MaxPooling2D((2, 2))
        
        # Bloc de features haut niveau
        self.conv3_1 = Conv2D(128, (3, 3), padding='same', activation='relu')
        self.conv3_2 = Conv2D(128, (3, 3), padding='same', activation='relu')
        self.bn3 = BatchNormalization()
        
        # Mécanisme d'attention
        self.attention = Conv2D(1, (1, 1), activation='sigmoid')
        
        # Classification
        self.global_pool = GlobalAveragePooling2D()
        self.dropout1 = Dropout(dropout_rate)
        self.dense1 = Dense(256, activation='relu')
        self.dropout2 = Dropout(dropout_rate * 0.7)
        self.dense2 = Dense(128, activation='relu')
        self.output_layer = Dense(num_classes, activation='softmax')
        
    def call(self, inputs, training=False):
        # Extraction de features
        x = self.conv1_1(inputs)
        x = self.conv1_2(x)
        x = self.bn1(x, training=training)
        x = self.pool1(x)
        
        x = self.conv2_1(x)
        x = self.conv2_2(x)
        x = self.bn2(x, training=training)
        x = self.pool2(x)
        
        x = self.conv3_1(x)
        x = self.conv3_2(x)
        x = self.bn3(x, training=training)
        
        # Application de l'attention
        attention_weights = self.attention(x)
        x = x * attention_weights
        
        # Classification
        x = self.global_pool(x)
        x = self.dropout1(x, training=training)
        x = self.dense1(x)
        x = self.dropout2(x, training=training)
        x = self.dense2(x)
        
        return self.output_layer(x)
    
    def get_attention_maps(self, inputs):
        """Retourne les cartes d'attention pour visualisation"""
        # Forward pass jusqu'à l'attention
        x = self.conv1_1(inputs)
        x = self.conv1_2(x)
        x = self.bn1(x, training=False)
        x = self.pool1(x)
        
        x = self.conv2_1(x)
        x = self.conv2_2(x)
        x = self.bn2(x, training=False)
        x = self.pool2(x)
        
        x = self.conv3_1(x)
        x = self.conv3_2(x)
        x = self.bn3(x, training=False)
        
        return self.attention(x)
\end{lstlisting}

\subsection{Entraînement et Optimisation}

\begin{lstlisting}[language=Python, caption=Pipeline d'entraînement avancé]
class ModelTrainer:
    def __init__(self, model: tf.keras.Model, config: TrainingConfig):
        self.model = model
        self.config = config
        self.history = {'train': [], 'val': []}
        
    def train(self, train_data, val_data, test_data):
        """Entraînement avec stratégies avancées"""
        
        # Callbacks
        callbacks = [
            # Réduction du learning rate
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            
            # Early stopping
            EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            
            # Checkpoint des meilleurs modèles
            ModelCheckpoint(
                'models/best_model_{epoch:02d}_{val_accuracy:.3f}.h5',
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            ),
            
            # TensorBoard
            TensorBoard(
                log_dir=f'logs/{datetime.now().strftime("%Y%m%d-%H%M%S")}',
                histogram_freq=1,
                write_graph=True,
                write_images=True,
                update_freq='epoch'
            ),
            
            # Custom callback pour métriques avancées
            CustomMetricsCallback(self.history)
        ]
        
        # Compilation avec optimiseur personnalisé
        self.model.compile(
            optimizer=self._get_optimizer(),
            loss='sparse_categorical_crossentropy',
            metrics=[
                'accuracy',
                tf.keras.metrics.Precision(),
                tf.keras.metrics.Recall(),
                tf.keras.metrics.AUC()
            ]
        )
        
        # Entraînement
        history = self.model.fit(
            train_data,
            validation_data=val_data,
            epochs=self.config.epochs,
            callbacks=callbacks,
            verbose=1
        )
        
        # Évaluation finale
        test_results = self.model.evaluate(test_data, verbose=0)
        
        return history, test_results
    
    def _get_optimizer(self):
        """Optimiseur avec warm-up et decay"""
        initial_learning_rate = self.config.learning_rate
        
        # Learning rate schedule avec warm-up
        def lr_schedule(epoch):
            if epoch < self.config.warmup_epochs:
                return initial_learning_rate * epoch / self.config.warmup_epochs
            else:
                return initial_learning_rate * tf.math.exp(
                    0.1 * (self.config.warmup_epochs - epoch)
                )
        
        return tf.keras.optimizers.Adam(
            learning_rate=tf.keras.callbacks.LearningRateScheduler(lr_schedule),
            beta_1=0.9,
            beta_2=0.999,
            epsilon=1e-8
        )
\end{lstlisting}

\section{Intégration et Tests}

\subsection{Tests Unitaires}

\begin{lstlisting}[language=Python, caption=Suite de tests pour le parser]
import pytest
import numpy as np
from unittest.mock import Mock, patch

class TestRawFileParser:
    @pytest.fixture
    def sample_file(self, tmp_path):
        """Crée un fichier .raw de test"""
        file_path = tmp_path / "test.raw"
        with open(file_path, 'wb') as f:
            # En-tête CON0
            f.write(b'CON0')
            f.write(struct.pack('<I', 1000))  # Taille
            f.write(struct.pack('<I', 0))     # Reserved
            
            # Datagramme RAW3
            f.write(b'RAW3')
            f.write(struct.pack('<I', 100))   # Taille
            f.write(struct.pack('<Q', int(time.time() * 1e6)))  # Timestamp
            
            # Données
            samples = np.random.randn(100).astype(np.float32)
            f.write(samples.tobytes())
            
            # CRC
            f.write(struct.pack('<I', 0x12345678))
        
        return file_path
    
    @pytest.mark.asyncio
    async def test_parse_valid_file(self, sample_file):
        parser = RawFileParser(str(sample_file))
        await parser.parse()
        
        assert len(parser.datagrams) > 0
        assert parser.datagrams[0]['type'] == 'samples'
        assert isinstance(parser.datagrams[0]['data'], np.ndarray)
    
    @pytest.mark.asyncio
    async def test_parse_corrupted_file(self, tmp_path):
        corrupted_file = tmp_path / "corrupted.raw"
        corrupted_file.write_bytes(b'INVALID_DATA')
        
        parser = RawFileParser(str(corrupted_file))
        with pytest.raises(ValueError, match="Invalid file format"):
            await parser.parse()
    
    def test_crc_validation(self, sample_file):
        # Test avec CRC invalide
        with open(sample_file, 'r+b') as f:
            f.seek(-4, 2)  # Aller à la fin
            f.write(struct.pack('<I', 0xDEADBEEF))  # CRC invalide
        
        parser = RawFileParser(str(sample_file))
        with patch('logging.Logger.warning') as mock_warning:
            parser.parse()
            mock_warning.assert_called_with("CRC mismatch at offset")
\end{lstlisting}

\subsection{Tests d'Intégration}

\begin{lstlisting}[language=Python, caption=Tests d'intégration API]
class TestAPIIntegration:
    @pytest.fixture
    def client(self):
        from fastapi.testclient import TestClient
        from main import app
        return TestClient(app)
    
    def test_classification_workflow(self, client, sample_raw_file):
        # 1. Upload du fichier
        with open(sample_raw_file, 'rb') as f:
            response = client.post(
                "/api/upload",
                files={"file": ("test.raw", f, "application/octet-stream")}
            )
        
        assert response.status_code == 200
        file_id = response.json()["file_id"]
        
        # 2. Génération de l'échogramme
        response = client.get(f"/api/echogram/{file_id}")
        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
        
        # 3. Classification
        response = client.post(
            f"/api/classify/{file_id}",
            json={"model_version": "v1.2"}
        )
        
        assert response.status_code == 200
        result = response.json()
        assert "predictions" in result
        assert len(result["predictions"]) == 3  # 3 espèces
        assert sum(p["confidence"] for p in result["predictions"]) == pytest.approx(1.0)
    
    def test_websocket_real_time(self, client):
        with client.websocket_connect("/ws") as websocket:
            # Envoi d'une requête de classification
            websocket.send_json({
                "type": "classify",
                "data": {"file_id": "test123"}
            })
            
            # Réception du résultat
            data = websocket.receive_json()
            assert data["type"] == "classification_result"
            assert "predictions" in data["data"]
\end{lstlisting}

\section{Défis Techniques et Solutions}

\subsection{Gestion de la Mémoire}

Le traitement de fichiers de plusieurs gigaoctets a nécessité des optimisations mémoire :

\begin{lstlisting}[language=Python, caption=Gestion mémoire optimisée]
class MemoryEfficientProcessor:
    def __init__(self, max_memory_mb=1024):
        self.max_memory = max_memory_mb * 1024 * 1024
        
    def process_large_file(self, file_path: str):
        """Traite des fichiers volumineux par chunks"""
        file_size = os.path.getsize(file_path)
        chunk_size = min(self.max_memory // 10, file_size)
        
        results = []
        
        with open(file_path, 'rb') as f:
            with tqdm(total=file_size, unit='B', unit_scale=True) as pbar:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    
                    # Traitement du chunk
                    result = self._process_chunk(chunk)
                    results.append(result)
                    
                    # Libération mémoire
                    del chunk
                    gc.collect()
                    
                    pbar.update(len(chunk))
        
        return self._merge_results(results)
\end{lstlisting}

\subsection{Performance en Production}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Métrique} & \textbf{Objectif} & \textbf{Réalisé} & \textbf{Status} \\
\hline
Latence API (p95) & <200ms & 156ms & ✓ \\
Throughput & 100 req/s & 142 req/s & ✓ \\
Temps classification & <1s & 0.8s & ✓ \\
Utilisation CPU & <80\% & 72\% & ✓ \\
Utilisation RAM & <4GB & 3.2GB & ✓ \\
\hline
\end{tabular}
\caption{Métriques de performance en production}
\label{tab:production_metrics}
\end{table}

\section{Conclusion du Chapitre}

Le développement de la solution a nécessité l'intégration harmonieuse de multiples technologies et l'implémentation d'algorithmes complexes. Les défis techniques, notamment en termes de performance et de gestion mémoire, ont été surmontés grâce à des optimisations ciblées et une architecture bien pensée.

La solution finale offre un pipeline complet depuis l'ingestion des données jusqu'à la classification, avec des performances qui dépassent les objectifs initiaux. L'architecture modulaire garantit l'évolutivité et la maintenabilité du système pour les développements futurs.

% ===== CHAPITRE 6 : RÉSULTATS ET VISUALISATIONS =====
\chapter{Résultats et Évaluation}

\section{Introduction}

Ce chapitre présente les résultats obtenus lors de l'évaluation de notre solution, incluant les performances des modèles de classification, les métriques système et les retours utilisateurs. Nous analysons également l'impact de notre solution sur le processus de travail des utilisateurs finaux.

\section{Performance des Modèles de Classification}

\subsection{Dataset d'Évaluation}

Le dataset utilisé pour l'évaluation comprend :

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Espèce} & \textbf{Train} & \textbf{Validation} & \textbf{Test} & \textbf{Total} \\
\hline
Anchois & 3,200 & 800 & 1,000 & 5,000 \\
Sardines & 2,800 & 700 & 875 & 4,375 \\
Thons & 2,400 & 600 & 750 & 3,750 \\
Autres & 1,600 & 400 & 500 & 2,500 \\
\hline
\textbf{Total} & 10,000 & 2,500 & 3,125 & 15,625 \\
\hline
\end{tabular}
\caption{Distribution du dataset par espèce}
\label{tab:dataset_distribution}
\end{table}

\subsection{Métriques de Classification}

\subsubsection{Matrice de Confusion}

\begin{figure}[h]
\centering
\begin{tikzpicture}[scale=0.8]
\matrix (m) [matrix of nodes,
    nodes={minimum size=1.5cm, anchor=center},
    column sep=0.1cm,
    row sep=0.1cm
] {
    & Anchois & Sardines & Thons & Autres \\
    Anchois & \node[fill=blue!80,text=white]{920}; & \node[fill=blue!20]{45}; & \node[fill=blue!20]{25}; & \node[fill=blue!20]{10}; \\
    Sardines & \node[fill=blue!20]{38}; & \node[fill=blue!80,text=white]{805}; & \node[fill=blue!20]{20}; & \node[fill=blue!20]{12}; \\
    Thons & \node[fill=blue!20]{15}; & \node[fill=blue!20]{18}; & \node[fill=blue!80,text=white]{702}; & \node[fill=blue!20]{15}; \\
    Autres & \node[fill=blue!20]{8}; & \node[fill=blue!20]{10}; & \node[fill=blue!20]{12}; & \node[fill=blue!80,text=white]{470}; \\
};

\node[above=0.5cm of m-1-2] {\textbf{Prédictions}};
\node[left=0.5cm of m-2-1, rotate=90, anchor=center] {\textbf{Réalité}};
\end{tikzpicture}
\caption{Matrice de confusion du modèle final}
\label{fig:confusion_matrix_final}
\end{figure}

\subsubsection{Métriques par Classe}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Classe} & \textbf{Précision} & \textbf{Rappel} & \textbf{F1-Score} & \textbf{Support} \\
\hline
Anchois & 0.92 & 0.92 & 0.92 & 1,000 \\
Sardines & 0.91 & 0.92 & 0.91 & 875 \\
Thons & 0.94 & 0.94 & 0.94 & 750 \\
Autres & 0.94 & 0.94 & 0.94 & 500 \\
\hline
\textbf{Moyenne} & 0.93 & 0.93 & 0.93 & 3,125 \\
\hline
\end{tabular}
\caption{Métriques détaillées par espèce}
\label{tab:metrics_by_species}
\end{table}

\subsection{Courbes d'Apprentissage}

\begin{figure}[h]
\centering
\begin{tikzpicture}
\begin{axis}[
    xlabel={Époque},
    ylabel={Accuracy},
    legend pos=south east,
    grid=major,
    width=12cm,
    height=8cm
]
\addplot[color=blue, mark=*] coordinates {
    (1,0.45) (2,0.62) (3,0.71) (4,0.78) (5,0.82)
    (6,0.85) (7,0.87) (8,0.89) (9,0.90) (10,0.91)
    (11,0.92) (12,0.92) (13,0.93) (14,0.93) (15,0.93)
};
\addlegendentry{Train}

\addplot[color=red, mark=square] coordinates {
    (1,0.42) (2,0.58) (3,0.68) (4,0.75) (5,0.79)
    (6,0.82) (7,0.84) (8,0.86) (9,0.88) (10,0.89)
    (11,0.90) (12,0.91) (13,0.92) (14,0.92) (15,0.92)
};
\addlegendentry{Validation}
\end{axis}
\end{tikzpicture}
\caption{Évolution de la précision durant l'entraînement}
\label{fig:learning_curves}
\end{figure}

\section{Analyse des Erreurs}

\subsection{Cas d'Erreurs Typiques}

Notre analyse des erreurs de classification révèle plusieurs patterns :

\begin{enumerate}
\item \textbf{Confusion Anchois-Sardines} (35\% des erreurs) :
    \begin{itemize}
    \item Tailles similaires des individus jeunes
    \item Signatures acoustiques proches à certaines fréquences
    \item Solution : Utilisation de multi-fréquences
    \end{itemize}

\item \textbf{Bruit de fond élevé} (25\% des erreurs) :
    \begin{itemize}
    \item Conditions météo défavorables
    \item Interférences d'autres navires
    \item Solution : Filtrage adaptatif amélioré
    \end{itemize}

\item \textbf{Bancs mixtes} (20\% des erreurs) :
    \begin{itemize}
    \item Plusieurs espèces mélangées
    \item Solution : Segmentation plus fine
    \end{itemize}
\end{enumerate}

\subsection{Visualisation des Cartes d'Attention}

Les cartes d'attention du modèle montrent que le réseau se concentre sur :
\begin{itemize}
\item Les zones de forte densité acoustique
\item Les transitions entre couches d'eau
\item Les patterns de mouvement caractéristiques
\end{itemize}

\section{Performance Système}

\subsection{Benchmarks de Charge}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Utilisateurs} & \textbf{Latence (ms)} & \textbf{CPU (\%)} & \textbf{RAM (GB)} & \textbf{Erreurs} \\
\hline
10 & 125 & 15 & 1.2 & 0 \\
50 & 142 & 35 & 2.1 & 0 \\
100 & 168 & 58 & 3.2 & 0 \\
200 & 215 & 78 & 4.5 & 0.1\% \\
500 & 380 & 92 & 6.8 & 0.8\% \\
\hline
\end{tabular}
\caption{Performance sous charge croissante}
\label{tab:load_testing}
\end{table}

\subsection{Temps de Traitement}

\begin{figure}[h]
\centering
\begin{tikzpicture}
\begin{axis}[
    ybar,
    xlabel={Étape du pipeline},
    ylabel={Temps (secondes)},
    symbolic x coords={Upload, Parsing, Calibration, Echogram, Classification, Total},
    xtick=data,
    nodes near coords,
    width=12cm,
    height=8cm
]
\addplot coordinates {
    (Upload, 0.5)
    (Parsing, 1.2)
    (Calibration, 0.8)
    (Echogram, 2.1)
    (Classification, 0.9)
    (Total, 5.5)
};
\end{axis}
\end{tikzpicture}
\caption{Temps de traitement par étape (fichier 1GB)}
\label{fig:processing_times}
\end{figure}

\section{Évaluation Utilisateur}

\subsection{Méthodologie}

Nous avons conduit une évaluation avec 15 utilisateurs experts :
\begin{itemize}
\item 5 biologistes marins
\item 5 techniciens acoustiques
\item 5 gestionnaires de pêcheries
\end{itemize}

\subsection{Résultats du Questionnaire}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|}
\hline
\textbf{Critère} & \textbf{Score (/5)} \\
\hline
Facilité d'utilisation & 4.6 \\
Précision des résultats & 4.4 \\
Rapidité du traitement & 4.8 \\
Qualité des visualisations & 4.7 \\
Utilité globale & 4.9 \\
\hline
\textbf{Moyenne} & 4.68 \\
\hline
\end{tabular}
\caption{Scores d'évaluation utilisateur}
\label{tab:user_evaluation}
\end{table}

\subsection{Retours Qualitatifs}

Les principaux retours positifs incluent :
\begin{itemize}
\item "Gain de temps considérable par rapport à l'analyse manuelle"
\item "Interface intuitive même pour les non-techniciens"
\item "Visualisations de qualité publication"
\item "Possibilité de retraiter rapidement avec différents paramètres"
\end{itemize}

Points d'amélioration suggérés :
\begin{itemize}
\item Ajout de plus d'espèces dans la classification
\item Export en formats additionnels (MATLAB, R)
\item Mode hors-ligne pour utilisation en mer
\item Tutoriels vidéo intégrés
\end{itemize}

\section{Impact Opérationnel}

\subsection{Comparaison Avant/Après}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Métrique} & \textbf{Avant} & \textbf{Après} & \textbf{Amélioration} \\
\hline
Temps d'analyse/jour & 8h & 1h & 87.5\% \\
Fichiers traités/jour & 10 & 150 & 15× \\
Précision identification & 85\% & 93\% & +8\% \\
Coût par analyse & 500€ & 30€ & 94\% \\
Délai de rapport & 1 semaine & 1 jour & 86\% \\
\hline
\end{tabular}
\caption{Impact opérationnel de la solution}
\label{tab:operational_impact}
\end{table}

\subsection{Retour sur Investissement}

Pour une organisation type traitant 200 jours de données par an :
\begin{itemize}
\item Économies annuelles : 94,000€
\item Coût de développement : 30,000€
\item ROI : 3.1× en première année
\item Temps de retour : 4 mois
\end{itemize}

\section{Cas d'Usage Réels}

\subsection{Campagne de Recherche NOAA}

Application lors de la campagne RL2107 :
\begin{itemize}
\item 45 jours de données traitées
\item 3,500 fichiers analysés
\item Identification de 12 zones de concentration
\item Réduction de 60\% du temps d'analyse
\end{itemize}

\subsection{Surveillance des Quotas}

Util