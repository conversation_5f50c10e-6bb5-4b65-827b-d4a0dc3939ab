% !TEX root = report_final_complete_v2.tex

\documentclass[12pt, a4paper]{report}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{titlesec}
\usepackage{xcolor}
\usepackage{ulem}
\usepackage{setspace}
\usepackage[colorlinks=true, linkcolor=black!70, urlcolor=black!70, citecolor=black!70]{hyperref}

\usepackage{listings}
\usepackage{tabularx}
\usepackage{float}
\usepackage{caption}
\usepackage{enumitem}
\usepackage{mdframed}
\usepackage{booktabs}
\usepackage{tikz}
\usetikzlibrary{shapes, arrows, positioning, fit, calc}
\usepackage{lmodern}
\usepackage{amsmath}
\usepackage{longtable}
\usepackage{multirow}
\usepackage{afterpage}
\usepackage{pdflscape}
\usepackage{calc} % For precise calculations
\usepackage{amssymb} % For checkmark and cross symbols

\usepackage[export]{adjustbox} % For better image positioning
\newcommand{\arabicphrase}[2][0.3]{%
  \centering\includegraphics[
    width=#1\textwidth,
    height=2.5em,
    keepaspectratio
  ]{#2}\par
}

% Page layout and styling
\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}
\definecolor{proteyusblue}{RGB}{0,82,155}
\titleformat{\chapter}[display]{\normalfont\huge\bfseries\color{proteyusblue}}{\chaptertitlename\ \thechapter}{20pt}{\Huge}
\titleformat{\section}{\normalfont\Large\bfseries\color{proteyusblue}}{\thesection}{1em}{}
\titleformat{\subsection}{\normalfont\large\bfseries\color{proteyusblue!80!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalfont\normalsize\bfseries\color{black!70}}{\thesubsubsection}{1em}{}
\onehalfspacing

% Code listing style
\lstdefinelanguage{JavaScript}{
  keywords={typeof, new, true, false, catch, function, return, null, catch, switch, var, if, in, while, do, else, case, break, const, let, await, async, yield, get, set},
  keywordstyle=\color{blue}\bfseries,
  ndkeywords={class, export, boolean, throw, implements, import, this, from, default, extends},
  ndkeywordstyle=\color{purple!80!black}\bfseries,
  identifierstyle=\color{black},
  sensitive=true,
  comment=[l]{//},
  morecomment=[s]{/*}{*/},
  commentstyle=\color{green!50!black}\ttfamily,
  stringstyle=\color{red}\ttfamily,
  morestring=[b]',
  morestring=[b]"
}

\lstdefinelanguage{yaml}{
  keywords={true,false,null,y,n},
  keywordstyle=\color{darkgray}\bfseries,
  ndkeywords={metadata,spec,replicas,selector,matchLabels,template,labels,containers,name,image,resources,requests,memory,cpu,limits,env,valueFrom,secretKeyRef,key,livenessProbe,httpGet,path,port,initialDelaySeconds,periodSeconds,service,ports,targetPort,type,apiVersion,kind},
  ndkeywordstyle=\color{blue}\bfseries,
  identifierstyle=\color{black},
  sensitive=false,
  comment=[l]{\#},
  morecomment=[s]{/*}{*/},
  commentstyle=\color{purple}\ttfamily,
  stringstyle=\color{red}\ttfamily,
  morestring=[b]',
  morestring=[b]"
}

\lstset{
    language=Python,
    basicstyle=\small\ttfamily,
    keywordstyle=\color{blue},
    commentstyle=\color{green!50!black},
    stringstyle=\color{red},
    numbers=left,
    numberstyle=\tiny\color{gray},
    stepnumber=1,
    numbersep=5pt,
    backgroundcolor=\color{white!98!black},
    frame=single,
    rulecolor=\color{black},
    tabsize=2,
    captionpos=b,
    breaklines=true,
    breakatwhitespace=false,
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    literate={é}{{\'e}}1 {è}{{\`e}}1 {à}{{\`a}}1 {ç}{{\c{c}}}1 {œ}{{\oe}}1 {ù}{{\`u}}1
             {É}{{\'E}}1 {È}{{\`E}}1 {À}{{\`A}}1 {Ç}{{\c{C}}}1 {Œ}{{\OE}}1 {Ù}{{\`U}}1
}

% Custom commands
\newcommand{\tech}[1]{\textbf{\textit{#1}}} % For technology names
\newcommand{\file}[1]{\texttt{#1}} % For file names
\newcommand{\code}[1]{\texttt{#1}} % For code snippets

\begin{document}

% ===== COVER PAGE (UNCHANGED) =====
\begin{titlepage}
\pagestyle{empty}
\begin{tikzpicture}[remember picture, overlay]
  \fill[green!60!black] (current page.north west) --
                        ([xshift=0cm,yshift=-6cm]current page.north west) --
                        ([xshift=7cm,yshift=0cm]current page.north west) -- cycle;
\end{tikzpicture}
\vspace*{-1.8cm}
\begin{flushright}
  \includegraphics[width=6cm]{emsi_logo.png}
  \includegraphics[width=4cm]{proteyus_logo.png}
\end{flushright}
\vspace{1.5cm}
\begin{center}
    \textbf{\LARGE RAPPORT DE STAGE DE FIN D'ÉTUDES} \\[0.3cm]
    \textit{\large 5\textsuperscript{ème} Année en Ingénierie Informatique et Réseaux}
\end{center}
\vspace{1.5cm}
\hrule
\vspace{1cm}
\begin{center}
    \textbf{\Huge Plateforme de Classification Acoustique Marine par IA} \\[0.5cm]
    \large Développement d'une solution complète pour l'analyse des écosystèmes marins
\end{center}
\vspace{1cm}
\hrule
\vspace{2.5cm}
\begin{flushleft}
    \textbf{\large Réalisé par :} \\[0.3cm]
    \normalsize ILYASS \& RACHOUADY
\end{flushleft}
\vspace{1.5cm}
\begin{flushleft}
    \textbf{\large Tuteur (s) :} \\[0.5cm]
    \textit{Encadrant Professionnel : Mr Stéphane Conti} \\[0.2cm]
    \textit{Encadrant Pédagogique : Mr Sayouti}
\end{flushleft}
\vfill
\begin{center}
\textbf{École Marocaine des Sciences de l'Ingénieur (EMSI)} \\
Année Universitaire 2024-2025
\end{center}
\end{titlepage}

% ===== INTRODUCTORY CONTENT =====
\pagestyle{plain}
\pagenumbering{roman}
\chapter*{Remerciements}
\addcontentsline{toc}{chapter}{Remerciements}
C'est avec une profonde reconnaissance que nous souhaitons exprimer notre gratitude envers toutes les personnes qui ont contribué, de près ou de loin, à la réussite de ce projet de fin d'études. Cette aventure n'aurait pas été possible sans leur soutien, leurs conseils et leur confiance.
Nous tenons tout d'abord à remercier très chaleureusement notre encadrant professionnel, \textbf{M. Stéphane Conti}, co-fondateur de Proteyus et expert émérite en acoustique sous-marine. Son accompagnement rigoureux, sa vision stratégique et sa disponibilité sans faille ont été des piliers tout au long de ce stage. Ses conseils avisés et son expertise technique inestimable ont été déterminants pour orienter nos recherches et surmonter les défis complexes de ce projet.
Nous exprimons également notre vive gratitude à \textbf{M. Amine Berraoui}, co-fondateur de Proteyus, pour nous avoir accueillis au sein de sa startup dynamique et pour la confiance qu'il nous a accordée. Son esprit d'entreprise et sa vision innovante ont créé un environnement de travail stimulant, propice à l'autonomie et à la créativité.
Nos sincères remerciements s'adressent à notre encadrant pédagogique, \textbf{M. Sayouti}, pour son suivi méthodologique, ses orientations académiques et son soutien constant. Sa rigueur et ses conseils nous ont permis de structurer notre démarche et de maintenir un haut niveau d'exigence académique.
Nous remercions également l'ensemble de l'équipe technique de Proteyus pour leur collaboration, leur partage de connaissances et l'excellente ambiance de travail qu'ils ont su instaurer. Leurs retours et leur soutien ont été précieux.
Enfin, nous adressons notre gratitude à l'administration et au corps professoral de l'École Marocaine des Sciences de l'Ingénieur (EMSI) pour la qualité de la formation reçue. Les compétences techniques et humaines acquises au cours de notre cursus ont été le socle qui nous a permis de mener à bien ce projet ambitieux et de nous projeter avec confiance vers notre avenir professionnel.

\chapter*{Résumé}
\addcontentsline{toc}{chapter}{Résumé}
Ce rapport présente le développement d'une solution intégrée et innovante pour la classification d'espèces marines, basée sur l'analyse par apprentissage profond des signaux d'échosondeurs. Mené au sein de la startup Proteyus sur une période de six mois (avril à septembre 2025), ce projet de fin d'études s'attaque à un défi majeur de la gestion durable des ressources halieutiques : l'identification automatisée, rapide et fiable de la biomasse marine.
Face à l'absence de solutions commerciales complètes et à la complexité d'accès aux jeux de données acoustiques publics, une approche novatrice a été entreprise. Le projet s'est articulé autour de trois contributions majeures : (1) la conception et le développement d'une plateforme web, \url{https://noaadataset.netlify.app/}, facilitant l'exploration, le filtrage et le téléchargement de plusieurs téraoctets de données acoustiques brutes du catalogue de la NOAA ; (2) la création d'une API backend robuste pour le traitement à la volée des fichiers \file{.raw}, la génération d'échogrammes et la correction radiométrique des signaux ; et (3) l'implémentation d'architectures de réseaux de neurones convolutionnels (CNN) pour la classification automatisée des espèces.
La solution met en œuvre des technologies de pointe telles que \tech{FastAPI} pour le backend asynchrone, \tech{React.js} pour une interface utilisateur réactive, et une version optimisée de \tech{PyEcholab} pour le traitement scientifique des signaux. Les modèles d'apprentissage profond, entraînés sur des échogrammes multi-fréquences, ont atteint une précision de classification de 92\% sur un ensemble de trois espèces pélagiques clés, démontrant la viabilité et l'efficacité de l'approche.
Ce travail constitue une contribution significative à la modernisation des outils d'analyse en bioacoustique marine. Il offre aux chercheurs, aux gestionnaires des pêcheries et aux acteurs de l'industrie un outil puissant pour la surveillance des écosystèmes, la réduction des prises accessoires et la promotion d'une pêche plus durable et sélective.
\vspace{1cm}
\noindent\textbf{Mots-clés :} Acoustique sous-marine, Apprentissage profond, CNN, Échosondeurs Simrad, Classification d'espèces, Traitement du signal, FastAPI, React.js, PyEcholab, Pêche durable, Bioacoustique.

\chapter*{Abstract}
\addcontentsline{toc}{chapter}{Abstract}
This report details the development of an innovative, integrated solution for marine species classification based on the deep learning analysis of echosounder signals. Conducted within the startup Proteyus over a six-month period (April to September 2025), this final year project addresses a major challenge in sustainable fisheries management: the automated, rapid, and reliable identification of marine biomass.
Confronted with the lack of comprehensive commercial solutions and the complexity of accessing public acoustic datasets, a novel approach was undertaken. The project was structured around three major contributions: (1) the design and development of a web platform, \url{https://noaadataset.netlify.app/}, to facilitate the exploration, filtering, and downloading of several terabytes of raw acoustic data from the NOAA catalog; (2) the creation of a robust backend API for on-the-fly processing of \file{.raw} files, echogram generation, and radiometric signal correction; and (3) the implementation of Convolutional Neural Network (CNN) architectures for automated species classification.
The solution leverages state-of-the-art technologies including \tech{FastAPI} for the asynchronous backend, \tech{React.js} for a responsive user interface, and an optimized version of \tech{PyEcholab} for scientific signal processing. The deep learning models, trained on multi-frequency echograms, achieved a classification accuracy of 92\% on a set of three key pelagic species, demonstrating the viability and effectiveness of the approach.
This work represents a significant contribution to the modernization of tools for marine bioacoustics analysis. It provides researchers, fisheries managers, and industry stakeholders with a powerful tool for ecosystem monitoring, bycatch reduction, and the promotion of more sustainable and selective fishing practices.
\vspace{1cm}
\noindent\textbf{Keywords:} Underwater Acoustics, Deep Learning, CNN, Simrad Echosounders, Species Classification, Signal Processing, FastAPI, React.js, PyEcholab, Sustainable Fisheries, Bioacoustics.

\tableofcontents
\listoffigures
\listoftables
\cleardoublepage

% ===== MAIN REPORT BODY =====
\pagenumbering{arabic}

\chapter{Introduction Générale}
\input{sections_ext/chapter1_introduction.tex}

\chapter{Étude du Besoin et Contexte Scientifique}
\input{sections_ext/chapter2_besoin.tex}

\chapter{État de l'Art et Positionnement du Projet}
\input{sections_ext/chapter3_etat_art.tex}

\chapter{Architecture et Choix Technologiques}
\input{sections_ext/chapter4_architecture.tex}

\chapter{Conception et Développement de la Solution}
\input{sections_ext/chapter5_developpement.tex}

\chapter{Protocole Expérimental et Résultats}
\input{sections_ext/chapter6_resultats.tex}

\chapter{Analyse Critique et Discussion}
\input{sections_ext/chapter7_discussion.tex}

\chapter{Conclusion Générale et Perspectives}
\input{sections_ext/chapter8_conclusion.tex}

% ===== ANNEXES & BIBLIOGRAPHY =====
\appendix
\chapter{Annexes}
\addcontentsline{toc}{chapter}{Annexes}
\section{Organigramme de Proteyus}
\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\textwidth]{organigramme_proteyus.png}
    \caption{Structure organisationnelle simplifiée de la startup Proteyus.}
\end{figure}

\section{Exemple de Curriculum Vitae}
\emph{(Placeholder pour le CV de l'étudiant)}

\chapter*{Bibliographie}
\addcontentsline{toc}{chapter}{Bibliographie}
\begin{itemize}
    \item[1] Urick, R. J. (1983). \textit{Principles of Underwater Sound}. McGraw-Hill.
    \item[2] Simmonds, J., \& MacLennan, D. N. (2005). \textit{Fisheries Acoustics: Theory and Practice}, (2nd ed.). Blackwell Science.
    \item[3] Korneliussen, R. J., et al. (2018). \textit{Acoustic identification of marine organisms}. ICES Cooperative Research Report No. 344.
    \item[4] LeCun, Y., Bengio, Y., \& Hinton, G. (2015). Deep learning. \textit{Nature}, 521(7553), 436-444.
    \item[5] Goodfellow, I., Bengio, Y., & Courville, A. (2016). \textit{Deep Learning}. MIT Press.
    \item[6] FastAPI Documentation. \url{https://fastapi.tiangolo.com/}
    \item[7] React Documentation. \url{https://reactjs.org/}
    \item[8] TensorFlow Documentation. \url{https://www.tensorflow.org/}
    \item[9] Echopype Documentation. \url{https://echopype.readthedocs.io/}
    \item[10] He, K., Zhang, X., Ren, S., & Sun, J. (2016). Deep Residual Learning for Image Recognition. In \textit{Proceedings of the IEEE conference on computer vision and pattern recognition} (pp. 770-778).
    \item[11] Selvaraju, R. R., et al. (2017). Grad-CAM: Visual Explanations from Deep Networks via Gradient-based Localization. In \textit{Proceedings of the IEEE international conference on computer vision} (pp. 618-626).
    \item[12] Kongsberg Maritime. (2020). \textit{Simrad EK80 Wideband Echosounder: Reference Manual}.
\end{itemize}

\end{document}