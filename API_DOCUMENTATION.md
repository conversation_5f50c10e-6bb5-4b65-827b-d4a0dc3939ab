# Marine Acoustic Analysis API Documentation

## Overview

The Marine Acoustic Analysis API is a comprehensive FastAPI-based system designed for processing, analyzing, and managing marine acoustic data from EK80 sonar files. The system provides advanced machine learning capabilities for acoustic pattern recognition and classification in marine environments.

**Base URL**: `http://localhost:8000` (development)  
**API Version**: 1.0.0  
**Framework**: FastAPI with automatic OpenAPI documentation  
**Documentation**: Available at `/docs` (Swagger UI) and `/redoc` (ReDoc)

## Table of Contents

- [System Architecture](#system-architecture)
- [Authentication](#authentication)
- [Core Services](#core-services)
- [API Endpoints](#api-endpoints)
- [Data Models](#data-models)
- [Error Handling](#error-handling)
- [Getting Started](#getting-started)

## System Architecture

The system is organized into 7 main service modules:

1. **Data Management Service** - File handling and storage
2. **Visualization Service** - Echogram and spectrogram generation  
3. **Feature Extraction Service** - Acoustic feature analysis
4. **Annotation Service** - Manual labeling and annotation management
5. **Dataset Service** - Training dataset organization
6. **ML Model Service** - Machine learning model training and prediction
7. **Analysis Service** - Performance analysis and model comparison

## Authentication

Currently, the API operates without authentication for development purposes. CORS is configured to allow requests from:
- `https://noaadataset.netlify.app`
- `http://localhost:3000`
- `http://localhost:5173`

## Core Services

### Data Sources
- **S3 Integration**: Direct access to NOAA marine acoustic datasets (`noaa-wcsd-pds` bucket)
- **Local File Support**: Processing of local RAW files
- **Multiple Formats**: Primary support for EK80 acoustic data format

### Performance Features
- **Lazy Loading**: Files are loaded on-demand to optimize memory usage
- **Streaming Responses**: Large visualizations are streamed to reduce memory footprint
- **Async Processing**: All endpoints are asynchronous for better concurrency

## API Endpoints

### System Health & Information

#### `GET /`
**Purpose**: Root endpoint providing API information and available endpoints

**Response**:
```json
{
  "message": "Marine Acoustic Analysis API",
  "version": "1.0.0",
  "endpoints": {
    "data": "/data",
    "visualization": "/visualize", 
    "features": "/features",
    "annotations": "/annotations",
    "datasets": "/datasets",
    "models": "/models",
    "analysis": "/analysis"
  }
}
```

#### `GET /health`
**Purpose**: Health check endpoint for monitoring system status

**Response**: 
```json
{"status": "healthy"}
```

---

### Data Management Service (`/data`)

Handles loading, managing, and accessing marine acoustic RAW files from various sources.

#### `POST /data/load-raw-file`
**Purpose**: Load a RAW file from S3 or local storage with lazy loading

**Request Body**:
```json
{
  "filename": "string",
  "source": "s3|local|url",
  "source_path": "string (optional)"
}
```

**Response**:
```json
{
  "file_id": "uuid",
  "filename": "string",
  "status": "success|error",
  "message": "string",
  "metadata": "object (optional)"
}
```

#### `GET /data/files`
**Purpose**: List all loaded/available RAW files

**Response**:
```json
[{
  "file_id": "uuid",
  "filename": "string", 
  "size": "integer",
  "metadata": "object"
}]
```

#### `GET /data/files/{file_id}`
**Purpose**: Get detailed information about a specific file

**Parameters**: 
- `file_id` (path parameter): UUID of the file

**Response**: Single file information object

#### `DELETE /data/files/{file_id}`
**Purpose**: Unload a file from memory (doesn't delete physical file)

**Parameters**: 
- `file_id` (path parameter): UUID of the file

**Response**: Status confirmation

#### `GET /data/files/{file_id}/channels`
**Purpose**: Get available acoustic channels for a specific file

**Parameters**: 
- `file_id` (path parameter): UUID of the file

**Response**: List of available channel IDs and their properties

---

### Visualization Service (`/visualize`)

Generates visual representations of acoustic data for analysis and presentation.

#### `POST /visualize/echogram`
**Purpose**: Generate echogram visualization from RAW acoustic data

**Request Body**:
```json
{
  "file_id": "uuid",
  "saturation_threshold": -10.0,
  "percentile_low": 1.0,
  "percentile_high": 99.0,
  "min_range_db": 20.0,
  "max_depth": 100.0
}
```

**Response**: Binary image data (PNG/JPEG) via StreamingResponse

**Description**: Creates depth vs. time acoustic intensity plots with configurable color scaling

#### `POST /visualize/spectrogram`
**Purpose**: Generate spectrogram visualization for frequency analysis

**Request Body**:
```json
{
  "file_id": "uuid",
  "ping_range": [start_ping, end_ping],
  "frequency": 38000,
  "window_size": 0.1,
  "overlap": 0.5
}
```

**Response**: Binary image data via StreamingResponse

**Description**: Time-frequency analysis with configurable window parameters

---

### Feature Extraction Service (`/features`)

Extracts acoustic features for machine learning and analysis.

#### `POST /features/extract`
**Purpose**: Extract acoustic features from RAW data for ML training/analysis

**Request Body**:
```json
{
  "file_id": "uuid",
  "ping_ranges": [[start1, end1], [start2, end2]],
  "frequencies": [38000, 120000],
  "window_sizes": [0.1, 0.2],
  "depth_range": [min_depth, max_depth],
  "feature_types": ["mfcc", "spectral_centroid", "bandwidth"]
}
```

**Response**:
```json
{
  "status": "success",
  "file_id": "uuid",
  "channel_id": "string",
  "features": "extracted_feature_data"
}
```

**Description**: Extracts MFCC, spectral features, and other acoustic characteristics

---

### Annotation Service (`/annotations`)

Manages manual annotations for training data and ground truth labeling.

#### `POST /annotations/`
**Purpose**: Create a new annotation (bounding box with label)

**Request Body**:
```json
{
  "file_id": "uuid",
  "x": 10.5,
  "y": 20.3,
  "width": 50.0,
  "height": 30.0,
  "label": "fish_school",
  "confidence": 0.95,
  "metadata": {}
}
```

**Response**: Created annotation object with assigned ID

#### `GET /annotations/{annotation_id}`
**Purpose**: Retrieve specific annotation by ID

**Parameters**: 
- `annotation_id` (path parameter): UUID of the annotation

#### `GET /annotations/file/{file_id}`
**Purpose**: Get all annotations for a specific file

**Parameters**: 
- `file_id` (path parameter): UUID of the file

#### `PUT /annotations/{annotation_id}`
**Purpose**: Update existing annotation

**Parameters**: 
- `annotation_id` (path parameter): UUID of the annotation

**Request Body**: Partial annotation update data

#### `DELETE /annotations/{annotation_id}`
**Purpose**: Delete an annotation

**Parameters**: 
- `annotation_id` (path parameter): UUID of the annotation

---

### Dataset Service (`/datasets`)

Organizes files and annotations into training datasets for machine learning.

#### `POST /datasets/`
**Purpose**: Create a new dataset combining files and annotations

**Request Body**:
```json
{
  "name": "Training Dataset v1",
  "description": "Fish detection training data",
  "file_ids": ["uuid1", "uuid2"],
  "annotation_ids": ["uuid3", "uuid4"]
}
```

#### `GET /datasets/`
**Purpose**: List all available datasets

#### `GET /datasets/{dataset_id}`
**Purpose**: Get specific dataset details

#### `PUT /datasets/{dataset_id}`
**Purpose**: Update dataset properties

#### `DELETE /datasets/{dataset_id}`
**Purpose**: Delete a dataset

#### `POST /datasets/{dataset_id}/files/{file_id}`
**Purpose**: Add a file to an existing dataset

#### `POST /datasets/{dataset_id}/annotations/{annotation_id}`
**Purpose**: Add an annotation to an existing dataset

---

### ML Model Service (`/models`)

Handles machine learning model training, management, and prediction.

#### `POST /models/train`
**Purpose**: Train a new machine learning model

**Request Body**:
```json
{
  "name": "Fish Detection CNN v1",
  "model_type": "cnn|lstm|random_forest",
  "dataset_id": "uuid",
  "hyperparameters": {},
  "validation_split": 0.2
}
```

**Response**: Trained model object with performance metrics

#### `GET /models/`
**Purpose**: List all trained models

#### `GET /models/{model_id}`
**Purpose**: Get specific model details and performance metrics

#### `DELETE /models/{model_id}`
**Purpose**: Delete a trained model

#### `POST /models/predict`
**Purpose**: Make predictions using a trained model

**Request Body**:
```json
{
  "model_id": "uuid",
  "file_id": "uuid", 
  "ping_range": [start, end],
  "confidence_threshold": 0.5
}
```

**Response**: Array of prediction objects
```json
[{
  "id": "uuid",
  "model_id": "uuid",
  "file_id": "uuid",
  "x": 10.5,
  "y": 20.3,
  "width": 50.0,
  "height": 30.0,
  "predicted_class": "fish_school",
  "confidence": 0.87,
  "created_at": "timestamp"
}]
```

---

### Analysis Service (`/analysis`)

Provides performance analysis and model comparison capabilities.

#### `GET /analysis/model-performance/{model_id}`
**Purpose**: Get detailed performance analysis for a specific model

**Parameters**: 
- `model_id` (path parameter): UUID of the model

#### `POST /analysis/predictions`
**Purpose**: Analyze a set of predictions for patterns and statistics

**Request Body**: Array of prediction objects

#### `POST /analysis/compare-models`
**Purpose**: Compare performance of multiple models

**Request Body**: Array of model IDs

**Response**: Comparative analysis results
```json
{
  "comparison_metrics": {},
  "best_model": "uuid",
  "performance_summary": {}
}
```

## Data Models

### Core Data Types

#### RawFileInfo
```json
{
  "file_id": "string",
  "filename": "string",
  "size": "integer",
  "last_modified": "datetime (optional)",
  "metadata": "object (optional)"
}
```

#### Annotation
```json
{
  "id": "string (optional)",
  "file_id": "string",
  "x": "float",
  "y": "float",
  "width": "float",
  "height": "float",
  "label": "string",
  "confidence": "float (optional)",
  "metadata": "object (optional)",
  "created_at": "datetime (optional)"
}
```

#### Dataset
```json
{
  "id": "string (optional)",
  "name": "string",
  "description": "string (optional)",
  "file_ids": ["string"],
  "annotation_ids": ["string"],
  "created_at": "datetime (optional)",
  "updated_at": "datetime (optional)"
}
```

#### MLModel
```json
{
  "id": "string (optional)",
  "name": "string",
  "model_type": "string",
  "architecture": "object (optional)",
  "hyperparameters": "object (optional)",
  "training_dataset_id": "string (optional)",
  "performance_metrics": "object (optional)",
  "created_at": "datetime (optional)",
  "updated_at": "datetime (optional)"
}
```

#### Prediction
```json
{
  "id": "string (optional)",
  "model_id": "string",
  "file_id": "string",
  "x": "float",
  "y": "float",
  "width": "float",
  "height": "float",
  "predicted_class": "string",
  "confidence": "float",
  "created_at": "datetime (optional)"
}
```

### Request Models

#### EchogramRequest
```json
{
  "file_id": "string",
  "saturation_threshold": "float (default: -10)",
  "percentile_low": "float (default: 1)",
  "percentile_high": "float (default: 99)",
  "min_range_db": "float (default: 20)",
  "max_depth": "float (default: 100)"
}
```

#### SpectrogramRequest
```json
{
  "file_id": "string",
  "ping_range": "[integer, integer]",
  "frequency": "integer (default: 38000)",
  "window_size": "float (default: 0.1)",
  "overlap": "float (default: 0.5)"
}
```

#### FeatureExtractionRequest
```json
{
  "file_id": "string",
  "ping_ranges": "[[integer, integer]]",
  "frequencies": "[integer]",
  "window_sizes": "[float]",
  "depth_range": "[float, float]",
  "feature_types": "[string] (default: ['mfcc', 'spectral_centroid', 'bandwidth'])"
}
```

#### TrainModelRequest
```json
{
  "name": "string",
  "model_type": "string",
  "dataset_id": "string",
  "hyperparameters": "object (optional)",
  "validation_split": "float (default: 0.2)"
}
```

#### PredictionRequest
```json
{
  "model_id": "string",
  "file_id": "string",
  "ping_range": "[integer, integer] (optional)",
  "confidence_threshold": "float (default: 0.5)"
}
```

## Error Handling

### HTTP Status Codes

- **200 OK**: Successful request
- **201 Created**: Resource successfully created
- **400 Bad Request**: Invalid request parameters or body
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server-side error

### Error Response Format

```json
{
  "error": "string",
  "message": "string",
  "details": "object (optional)"
}
```

### Common Error Scenarios

1. **File Not Found**: When requesting operations on non-existent file IDs
2. **Invalid Parameters**: When request parameters don't meet validation requirements
3. **Processing Errors**: When acoustic data processing fails
4. **Model Training Failures**: When ML model training encounters errors
5. **Memory Issues**: When lazy loading fails due to file access problems

## Getting Started

### Prerequisites

- Python 3.8+
- FastAPI
- Required dependencies (see `requirements.txt`)

### Installation

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Configure settings in `app/core/config.py`
4. Run the server: `uvicorn app.main:app --reload`

### Configuration

Key configuration options in `app/core/config.py`:

- **S3_BUCKET_NAME**: NOAA data bucket (`noaa-wcsd-pds`)
- **S3_PREFIX**: Path to EK80 data (`data/raw/Reuben_Lasker/RL2107/EK80`)
- **LOCAL_RAW_DIR**: Local directory for RAW files
- **ALLOWED_ORIGINS**: CORS allowed origins
- **MODELS_DIR**: Directory for storing trained models

### Quick Start Example

1. **Load a file**:
```bash
curl -X POST "http://localhost:8000/data/load-raw-file" \
  -H "Content-Type: application/json" \
  -d '{"filename": "example.raw", "source": "local"}'
```

2. **Generate echogram**:
```bash
curl -X POST "http://localhost:8000/visualize/echogram" \
  -H "Content-Type: application/json" \
  -d '{"file_id": "your-file-id"}'
```

3. **Extract features**:
```bash
curl -X POST "http://localhost:8000/features/extract" \
  -H "Content-Type: application/json" \
  -d '{
    "file_id": "your-file-id",
    "ping_ranges": [[0, 100]],
    "frequencies": [38000],
    "window_sizes": [0.1],
    "depth_range": [0, 100],
    "feature_types": ["mfcc"]
  }'
```

### API Documentation Access

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI JSON**: `http://localhost:8000/openapi.json`

## Machine Learning Pipeline

### Supported Model Types

- **CNN**: Convolutional Neural Networks for image-based acoustic analysis
- **LSTM**: Long Short-Term Memory networks for temporal pattern recognition
- **Random Forest**: Traditional ML for feature-based classification

### Feature Types

- **MFCC**: Mel-Frequency Cepstral Coefficients
- **Spectral Centroid**: Center of mass of the spectrum
- **Bandwidth**: Spectral bandwidth measurements
- **Custom Features**: Extensible feature extraction framework

### Training Workflow

1. Load and prepare acoustic data files
2. Create annotations for ground truth labeling
3. Organize data into training datasets
4. Extract relevant acoustic features
5. Train ML models with specified hyperparameters
6. Evaluate model performance
7. Use trained models for prediction on new data

## Performance Considerations

### Memory Management

- **Lazy Loading**: Files are loaded only when needed
- **Streaming**: Large responses are streamed to reduce memory usage
- **Cleanup**: Automatic cleanup of unused file data

### Scalability

- **Async Processing**: All endpoints support concurrent requests
- **Modular Architecture**: Services can be scaled independently
- **Caching**: Intelligent caching of processed data and features

### Optimization Tips

1. Use appropriate ping ranges to limit processing scope
2. Configure visualization parameters to balance quality and performance
3. Leverage lazy loading by accessing files only when needed
4. Monitor memory usage when processing large datasets

---

## Support and Contributing

For questions, issues, or contributions, please refer to the project repository and documentation.

**API Version**: 1.0.0
**Last Updated**: 2025
**Framework**: FastAPI with Python 3.8+
