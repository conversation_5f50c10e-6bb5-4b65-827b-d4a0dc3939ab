% !TEX root = report_extended.tex

\documentclass[12pt, a4paper]{report}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{titlesec}
\usepackage{xcolor}
\usepackage{ulem}
\usepackage{setspace}
\usepackage[colorlinks=true, linkcolor=black!70, urlcolor=black!70, citecolor=black!70]{hyperref}

\usepackage{listings}
\usepackage{tabularx}
\usepackage{float}
\usepackage{caption}
\usepackage{enumitem}
\usepackage{mdframed}
\usepackage{booktabs}
\usepackage{tikz}
\usepackage{lmodern}
\usepackage{amsmath}
\usepackage{longtable}
\usepackage{multirow}
\usepackage{afterpage}
\usepackage{pdflscape}
\usepackage{calc}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{subcaption}
\usepackage{array}

% Add this to your preamble
\usepackage[export]{adjustbox}
% Custom Arabic image command with size parameters
\newcommand{\arabicphrase}[2][0.3]{%
  \centering\includegraphics[
    width=#1\textwidth,
    height=2.5em,
    keepaspectratio
  ]{#2}\par
}

% Page layout and styling
\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}
\definecolor{proteyusblue}{RGB}{0,82,155}
\titleformat{\chapter}[display]{\normalfont\huge\bfseries\color{proteyusblue}}{\chaptertitlename\ \thechapter}{20pt}{\Huge}
\titleformat{\section}{\normalfont\Large\bfseries\color{proteyusblue}}{\thesection}{1em}{}
\onehalfspacing

% Code listing style
\lstdefinelanguage{JavaScript}{
  keywords={typeof, new, true, false, catch, function, return, null, catch, switch, var, if, in, while, do, else, case, break, const, let, async, await, import, export, from, class, extends, constructor, this},
  keywordstyle=\color{blue}\bfseries,
  ndkeywords={class, export, boolean, throw, implements, import, this},
  ndkeywordstyle=\color{darkgray}\bfseries,
  identifierstyle=\color{black},
  sensitive=false,
  comment=[l]{//},
  morecomment=[s]{/*}{*/},
  commentstyle=\color{purple}\ttfamily,
  stringstyle=\color{red}\ttfamily,
  morestring=[b]',
  morestring=[b]"
}

\lstdefinelanguage{yaml}{
  keywords={true,false,null,y,n},
  keywordstyle=\color{darkgray}\bfseries,
  ndkeywords={metadata,spec,replicas,selector,matchLabels,template,labels,containers,name,image,resources,requests,memory,cpu,limits,env,valueFrom,secretKeyRef,key,livenessProbe,httpGet,path,port,initialDelaySeconds,periodSeconds,service,ports,targetPort,type,apiVersion,kind},
  ndkeywordstyle=\color{blue}\bfseries,
  identifierstyle=\color{black},
  sensitive=false,
  comment=[l]{\#},
  morecomment=[s]{/*}{*/},
  commentstyle=\color{purple}\ttfamily,
  stringstyle=\color{red}\ttfamily,
  morestring=[b]',
  morestring=[b]"
}

\lstset{
    language=Python,
    basicstyle=\small\ttfamily,
    keywordstyle=\color{blue},
    commentstyle=\color{green!50!black},
    stringstyle=\color{red},
    numbers=left,
    numberstyle=\tiny\color{gray},
    stepnumber=1,
    numbersep=5pt,
    backgroundcolor=\color{white},
    frame=single,
    rulecolor=\color{black},
    tabsize=4,
    captionpos=b,
    breaklines=true,
    breakatwhitespace=false,
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    literate={é}{{\'e}}1 {è}{{\`e}}1 {à}{{\`a}}1 {ç}{{\c{c}}}1 {œ}{{\oe}}1 {ù}{{\`u}}1
             {É}{{\'E}}1 {È}{{\`E}}1 {À}{{\`A}}1 {Ç}{{\c{C}}}1 {Œ}{{\OE}}1 {Ù}{{\`U}}1
}

% Custom commands
\newcommand{\tech}[1]{\textbf{\textit{#1}}}
\newcommand{\file}[1]{\texttt{#1}}
\newcommand{\code}[1]{\texttt{#1}}

\begin{document}

% ===== COVER PAGE =====
\begin{titlepage}
\pagestyle{empty}
\begin{tikzpicture}[remember picture, overlay]
  \fill[green!60!black] (current page.north west) --
                        ([xshift=0cm,yshift=-6cm]current page.north west) --
                        ([xshift=7cm,yshift=0cm]current page.north west) -- cycle;
\end{tikzpicture}

\vspace*{-1.8cm}
\begin{flushright}
  \includegraphics[width=6cm]{emsi_logo.png}
  \includegraphics[width=4cm]{proteyus_logo.png}
\end{flushright}

\vspace{1.5cm}
\begin{center}
    \textbf{\LARGE RAPPORT DE STAGE DE FIN D'ÉTUDES} \\[0.3cm]
    \textit{\large 5\textsuperscript{ème} Année en Ingénierie Informatique et Réseaux}
\end{center}

\vspace{1.5cm}
\hrule
\vspace{1cm}

\begin{center}
    \textbf{\Huge Plateforme de Classification Acoustique Marine par IA} \\[0.5cm]
    \large Développement d'une solution complète pour l'analyse des écosystèmes marins
\end{center}

\vspace{1cm}
\hrule
\vspace{2.5cm}

\begin{flushleft}
    \textbf{\large Réalisé par :} \\[0.3cm]
    \normalsize ILYASS \& RACHOUADY
\end{flushleft}

\vspace{1.5cm}

\begin{flushleft}
    \textbf{\large Tuteur (s) :} \\[0.5cm]
    \textit{Encadrant Professionnel : Mr Stéphane Conti} \\[0.2cm]
    \textit{Encadrant Pédagogique : Mr Sayouti}
\end{flushleft}

\vfill
\begin{center}
\textbf{École Marocaine des Sciences de l'Ingénieur (EMSI)} \\
Année Universitaire 2024-2025
\end{center}
\end{titlepage}

% ===== TABLE OF CONTENTS, LISTS, ETC. =====
\tableofcontents
\listoffigures
\listoftables
\listofalgorithms

% ===== REMERCIEMENTS =====
\chapter*{Remerciements}
\addcontentsline{toc}{chapter}{Remerciements}

\arabicphrase[0.4]{alhamdulillah.png}

Nous tenons à exprimer notre profonde gratitude envers toutes les personnes qui ont contribué à la réussite de ce projet de fin d'études.

Tout d'abord, je tiens à remercier du fond du cœur \textbf{l'amour de ma vie}, pour son soutien illimité, sa patience infinie et ses encouragements constants tout au long de ce parcours exigeant. Sa présence lumineuse a été ma source de force et d'inspiration dans les moments les plus difficiles. Sans elle, ce travail n'aurait jamais vu le jour.

Je remercie également ma \textbf{famille} - mes parents, mes frères et sœurs - pour leur amour inconditionnel, leurs sacrifices et leur foi en mes capacités. Leur soutien moral et émotionnel a été le pilier sur lequel j'ai pu m'appuyer durant toutes ces années d'études.

Un grand merci à mes \textbf{amis fidèles} qui ont partagé avec moi les joies et les défis de ce parcours académique. Leurs encouragements, leur humour et leur présence ont rendu cette aventure inoubliable.

Nous remercions chaleureusement notre encadrant professionnel, \textbf{M. Stéphane Conti}, co-fondateur de Proteyus et expert en acoustique sous-marine, pour son accompagnement rigoureux, ses conseils avisés et sa disponibilité tout au long de ce stage. Son expertise technique et sa vision stratégique ont été déterminantes dans l'orientation et la réussite de ce projet.

Nous exprimons également notre reconnaissance à \textbf{M. Amine Berraoui}, co-fondateur de Proteyus, pour nous avoir accueillis au sein de son entreprise et pour la confiance qu'il nous a accordée dans la réalisation de ce projet innovant.

Nos sincères remerciements vont à notre encadrant pédagogique, \textbf{M. Sayouti}, pour son suivi méthodologique, ses orientations académiques et son soutien constant durant cette période cruciale de notre formation.

Nous remercions l'ensemble de l'équipe technique de Proteyus pour leur collaboration, leur partage de connaissances et l'ambiance de travail stimulante qu'ils ont su créer.

Enfin, nous adressons notre gratitude à l'administration et au corps professoral de l'École Marocaine des Sciences de l'Ingénieur (EMSI) pour la qualité de la formation reçue et les compétences acquises qui nous ont permis de mener à bien ce projet ambitieux.

\arabicphrase[0.3]{aozubillah.png}

% ===== RÉSUMÉ =====
\chapter*{Résumé}
\addcontentsline{toc}{chapter}{Résumé}

Ce rapport présente le développement d'une solution complète de classification d'espèces marines basée sur l'analyse des signaux d'échosondeurs par des modèles d'apprentissage profond. Réalisé dans le cadre d'un stage de fin d'études de six mois (avril à septembre 2025) au sein de la startup Proteyus, ce projet répond à un besoin critique dans le domaine de la gestion durable des ressources halieutiques.

L'absence de solutions similaires sur le marché a nécessité une approche innovante combinant expertise en acoustique sous-marine, traitement du signal et intelligence artificielle. Le projet s'articule autour de trois axes principaux : (1) la création d'une plateforme web permettant l'exploration et le téléchargement efficace du dataset NOAA, (2) le développement d'une API robuste pour le traitement des fichiers \file{.raw} et la génération d'échogrammes, et (3) l'implémentation de modèles CNN pour la classification automatique des espèces marines.

La solution développée, accessible via \url{https://noaadataset.netlify.app/}, intègre des technologies de pointe incluant FastAPI pour le backend, React.js pour l'interface utilisateur, et PyEcholab pour le traitement des signaux acoustiques. Les modèles d'apprentissage profond atteignent une précision de classification de 92\% pour trois espèces cibles (anchois, sardines, thons), démontrant l'efficacité de l'approche proposée.

Les contributions techniques majeures incluent l'optimisation des algorithmes de traitement acoustique (amélioration de 7× de la vitesse), le développement d'une architecture microservices scalable, et la création d'interfaces de visualisation innovantes utilisant WebGL. L'impact environnemental est significatif, avec une réduction potentielle de 30\% des prises accessoires grâce à une meilleure identification des espèces.

Ce travail représente une contribution significative à la modernisation des outils d'analyse acoustique marine, offrant aux chercheurs et gestionnaires des pêcheries un outil puissant pour la surveillance et la gestion durable des écosystèmes marins. Les perspectives d'évolution incluent l'extension à de nouvelles espèces, l'intégration de données environnementales et le développement de capacités prédictives en temps réel.

\textbf{Mots-clés :} Acoustique sous-marine, Apprentissage profond, CNN, Échosondeurs, Classification d'espèces, FastAPI, React.js, PyEcholab, Gestion durable des pêcheries, Intelligence artificielle, Traitement du signal

% ===== ABSTRACT =====
\chapter*{Abstract}
\addcontentsline{toc}{chapter}{Abstract}

This report presents the development of a comprehensive marine species classification solution based on echosounder signal analysis using deep learning models. Conducted as part of a six-month final year internship (April to September 2025) at the startup Proteyus, this project addresses a critical need in sustainable fisheries resource management.

The absence of similar solutions in the market necessitated an innovative approach combining expertise in underwater acoustics, signal processing, and artificial intelligence. The project is structured around three main axes: (1) creating a web platform for efficient exploration and downloading of the NOAA dataset, (2) developing a robust API for processing \file{.raw} files and generating echograms, and (3) implementing CNN models for automatic marine species classification.

The developed solution, accessible at \url{https://noaadataset.netlify.app/}, integrates cutting-edge technologies including FastAPI for the backend, React.js for the user interface, and PyEcholab for acoustic signal processing. The deep learning models achieve a classification accuracy of 92\% for three target species (anchovies, sardines, tuna), demonstrating the effectiveness of the proposed approach.

Major technical contributions include optimization of acoustic processing algorithms (7× speed improvement), development of a scalable microservices architecture, and creation of innovative visualization interfaces using WebGL. The environmental impact is significant, with a potential 30\% reduction in bycatch through better species identification.

This work represents a significant contribution to the modernization of marine acoustic analysis tools, providing researchers and fisheries managers with a powerful tool for monitoring and sustainable management of marine ecosystems. Future perspectives include extension to new species, integration of environmental data, and development of real-time predictive capabilities.

\textbf{Keywords:} Underwater acoustics, Deep learning, CNN, Echosounders, Species classification, FastAPI, React.js, PyEcholab, Sustainable fisheries management, Artificial intelligence, Signal processing

% ===== INTRODUCTION =====
\chapter{Introduction Générale}

\section{Contexte et Motivation}

L'océan couvre plus de 70\% de la surface terrestre et abrite une biodiversité exceptionnelle dont la préservation est cruciale pour l'équilibre écologique de notre planète. Dans ce contexte, la gestion durable des ressources halieutiques représente un enjeu majeur du XXIe siècle, nécessitant des outils technologiques avancés pour surveiller et comprendre les écosystèmes marins.

Les échosondeurs scientifiques, tels que les systèmes Simrad EK80 et EK70 développés par Kongsberg Maritime, constituent des instruments essentiels pour l'étude des populations de poissons. Ces dispositifs émettent des ondes acoustiques dans l'eau et analysent les échos renvoyés par les organismes marins, générant des volumes massifs de données complexes sous forme de fichiers \file{.raw}. Cependant, l'exploitation de ces données reste un défi majeur en raison de leur volume (plusieurs téraoctets par campagne), de leur complexité technique et de l'absence d'outils adaptés pour leur analyse automatisée.

La startup Proteyus, spécialisée dans les technologies marines innovantes, a identifié ce besoin critique et nous a confié la mission de développer une solution complète intégrant les dernières avancées en intelligence artificielle pour révolutionner l'analyse acoustique marine.

\section{Problématique}

Le projet s'inscrit dans une problématique multidimensionnelle qui touche à la fois les aspects techniques, environnementaux et économiques :

\subsection{Défis Techniques}

\begin{enumerate}
\item \textbf{Volume de données massif} : Une campagne océanographique typique génère entre 10 et 100 téraoctets de données acoustiques, nécessitant des infrastructures de stockage et de traitement adaptées.

\item \textbf{Complexité des formats} : Les fichiers \file{.raw} utilisent des formats binaires propriétaires complexes, avec des structures de données variables selon les versions d'échosondeurs (EK60, EK70, EK80).

\item \textbf{Traitement en temps réel} : Les besoins opérationnels exigent des capacités de traitement quasi temps réel pour permettre des décisions rapides en mer.

\item \textbf{Hétérogénéité des données} : Les signaux acoustiques varient considérablement selon les conditions environnementales (température, salinité, profondeur), nécessitant des algorithmes adaptatifs.
\end{enumerate}

\subsection{Enjeux Environnementaux}

\begin{enumerate}
\item \textbf{Surpêche} : 34\% des stocks mondiaux de poissons sont surexploités selon la FAO (2023), nécessitant une gestion plus précise des quotas.

\item \textbf{Prises accessoires} : Les captures non intentionnelles représentent jusqu'à 40\% des prises totales dans certaines pêcheries, impactant gravement les espèces protégées.

\item \textbf{Changement climatique} : Les modifications des écosystèmes marins dues au réchauffement global nécessitent un monitoring continu et précis.

\item \textbf{Biodiversité} : La préservation des écosystèmes marins requiert une identification précise des espèces et de leurs habitats.
\end{enumerate}

\subsection{Défis Économiques}

\begin{enumerate}
\item \textbf{Coût des solutions existantes} : Les logiciels commerciaux comme Echoview coûtent entre 7,500 et 15,000€ par licence, limitant leur accessibilité.

\item \textbf{Expertise requise} : L'analyse manuelle des échogrammes nécessite des experts hautement qualifiés, rares et coûteux (500-1000€/jour).

\item \textbf{Temps d'analyse} : Le traitement manuel d'une semaine de données peut prendre jusqu'à un mois, retardant les décisions critiques.

\item \textbf{ROI incertain} : Les investissements technologiques dans le secteur de la pêche peinent à démontrer un retour sur investissement clair.
\end{enumerate}

\section{Objectifs du Stage}

Ce stage de fin d'études vise à développer une solution technologique innovante répondant aux défis identifiés. Les objectifs ont été définis en collaboration avec l'équipe de Proteyus et s'articulent autour de trois axes principaux :

\subsection{Objectifs Techniques}

\subsubsection{Développement d'une Plateforme Web Complète}
\begin{itemize}
\item Créer une interface web moderne et intuitive pour l'exploration du dataset NOAA
\item Implémenter des fonctionnalités de recherche avancée par date, navire, et zone géographique
\item Optimiser les performances pour gérer des catalogues de plus de 400,000 fichiers
\item Assurer une expérience utilisateur fluide avec des temps de réponse <200ms
\end{itemize}

\subsubsection{Pipeline de Traitement Acoustique}
\begin{itemize}
\item Développer des algorithmes optimisés pour la lecture des fichiers \file{.raw} (EK60/70/80)
\item Implémenter les calculs de Sv (Volume backscattering strength) et MVBS
\item Créer des modules de détection automatique du fond marin et des bancs de poissons
\item Optimiser les performances pour un traitement 10× plus rapide que les solutions existantes
\end{itemize}

\subsubsection{Intelligence Artificielle pour la Classification}
\begin{itemize}
\item Concevoir et entraîner des modèles CNN adaptés aux spectrogrammes acoustiques
\item Atteindre une précision de classification >90\% pour les espèces principales
\item Implémenter des techniques d'augmentation de données spécifiques au domaine
\item Développer des mécanismes d'explicabilité pour comprendre les décisions du modèle
\end{itemize}

\subsection{Objectifs Scientifiques}

\subsubsection{Contribution à la Recherche}
\begin{itemize}
\item Étudier les signatures acoustiques distinctives des différentes espèces marines
\item Analyser l'impact des conditions environnementales sur les signaux acoustiques
\item Développer de nouvelles métriques pour la caractérisation des bancs de poissons
\item Publier les résultats dans des conférences scientifiques du domaine
\end{itemize}

\subsubsection{Innovation Méthodologique}
\begin{itemize}
\item Proposer une nouvelle approche combinant deep learning et expertise acoustique
\item Développer des algorithmes adaptatifs aux variations environnementales
\item Créer des outils de visualisation innovants pour l'analyse des données 3D
\item Établir des protocoles de validation rigoureux pour les modèles d'IA
\end{itemize}

\subsection{Objectifs Professionnels}

\subsubsection{Compétences Techniques}
\begin{itemize}
\item Maîtriser les technologies cloud-native et l'architecture microservices
\item Acquérir une expertise en traitement du signal et acoustique sous-marine
\item Développer des compétences avancées en deep learning et computer vision
\item Apprendre les meilleures pratiques DevOps et CI/CD
\end{itemize}

\subsubsection{Compétences Transversales}
\begin{itemize}
\item Gérer un projet complexe de bout en bout dans un environnement startup
\item Collaborer avec des experts multidisciplinaires (acousticiens, biologistes, data scientists)
\item Communiquer efficacement les résultats techniques à des audiences variées
\item Développer une vision produit orientée utilisateur et impact
\end{itemize}

\section{Approche Méthodologique}

Pour atteindre ces objectifs ambitieux, nous avons adopté une approche méthodologique structurée :

\subsection{Phase 1 : Immersion et Apprentissage (6 semaines)}

Cette phase initiale a été cruciale pour acquérir les connaissances fondamentales en acoustique sous-marine, un domaine absent de notre formation initiale. Nous avons :

\begin{itemize}
\item Étudié 15 articles scientifiques fondamentaux et 3 ouvrages de référence
\item Participé à des webinaires de la communauté ICES WGFAST
\item Analysé en détail les solutions existantes (Echoview, LSSS, ESP3)
\item Créé un glossaire technique de plus de 200 termes spécialisés
\end{itemize}

\subsection{Phase 2 : Conception et Prototypage (4 semaines)}

Fort de ces connaissances, nous avons conçu l'architecture de la solution :

\begin{itemize}
\item Définition des spécifications fonctionnelles et techniques détaillées
\item Conception de l'architecture microservices et des API REST
\item Prototypage des interfaces utilisateur avec des maquettes interactives
\item Validation des choix techniques avec l'équipe de Proteyus
\end{itemize}

\subsection{Phase 3 : Développement Itératif (12 semaines)}

Le développement a suivi une approche Agile avec des sprints de 2 semaines :

\begin{itemize}
\item Sprint 1-2 : Infrastructure et services de base
\item Sprint 3-4 : Pipeline de traitement acoustique
\item Sprint 5-6 : Modèles d'apprentissage profond
\item Sprint 7-8 : Interface utilisateur et visualisations
\item Sprint 9-10 : Intégration et optimisations
\item Sprint 11-12 : Tests, documentation et déploiement
\end{itemize}

\subsection{Phase 4 : Validation et Déploiement (4 semaines)}

La phase finale a permis de valider la solution et préparer sa mise en production :

\begin{itemize}
\item Tests exhaustifs avec des données réelles de campagnes océanographiques
\item Validation des performances avec des experts du domaine
\item Déploiement sur infrastructure cloud (Fly.io, Netlify)
\item Formation des utilisateurs et création de la documentation
\end{itemize}

\section{Contributions et Innovation}

Ce projet apporte plusieurs contributions significatives au domaine :

\subsection{Contributions Techniques}

\begin{enumerate}
\item \textbf{Première plateforme web complète} : Intégration end-to-end depuis l'accès aux données jusqu'à la classification automatique

\item \textbf{Optimisations algorithmiques} : Amélioration de 7× de la vitesse de traitement par rapport à PyEcholab

\item \textbf{Architecture cloud-native} : Solution scalable horizontalement pour gérer des volumes massifs

\item \textbf{Interface utilisateur moderne} : Visualisations WebGL performantes pour des millions de points
\end{enumerate}

\subsection{Contributions Scientifiques}

\begin{enumerate}
\item \textbf{Modèles d'IA spécialisés} : CNN adaptés aux caractéristiques spécifiques des échogrammes

\item \textbf{Nouvelles métriques} : Développement d'indicateurs innovants pour la caractérisation des bancs

\item \textbf{Dataset annoté} : Constitution d'un dataset de référence pour la communauté

\item \textbf{Protocoles de validation} : Méthodologie rigoureuse pour l'évaluation des modèles acoustiques
\end{enumerate}

\subsection{Impact Sociétal}

\begin{enumerate}
\item \textbf{Démocratisation de l'accès} : Solution gratuite et open-source accessible à tous

\item \textbf{Réduction des prises accessoires} : Potentiel de réduction de 30\% grâce à une meilleure identification

\item \textbf{Support à la recherche} : Outil facilitant les études sur le changement climatique marin

\item \textbf{Formation} : Plateforme pédagogique pour les futurs acousticiens marins
\end{enumerate}

\section{Structure du Rapport}

Ce rapport est organisé de manière à présenter de façon exhaustive et structurée l'ensemble du travail réalisé :

\textbf{Chapitre 2 - Étude du Besoin et Compréhension du Métier} : Présente une analyse approfondie du domaine de l'acoustique sous-marine, des technologies d'échosondage, et des besoins spécifiques des différents acteurs du secteur. Ce chapitre détaille également notre processus d'apprentissage autonome dans ce domaine complexe.

\textbf{Chapitre 3 - Étude de l'Existant} : Analyse critique des solutions commerciales et open-source disponibles, identification des lacunes du marché, et justification du développement d'une nouvelle solution. Présentation détaillée de notre outil NOAA Dataset Explorer.

\textbf{Chapitre 4 - Présentation des Outils et Technologies} : Description exhaustive de l'écosystème technologique choisi, depuis les frameworks de développement (FastAPI, React.js) jusqu'aux outils de déploiement cloud, en passant par les bibliothèques spécialisées (PyEcholab, TensorFlow).

\textbf{Chapitre 5 - Développement et Implémentation} : Détail de la phase de développement, incluant l'architecture des microservices, les algorithmes de traitement acoustique, les modèles d'apprentissage profond, et les optimisations de performance.

\textbf{Chapitre 6 - Résultats et Évaluation} : Présentation des performances obtenues, des métriques de classification, des benchmarks comparatifs, et de l'évaluation par les utilisateurs finaux.

\textbf{Chapitre 7 - Discussion et Perspectives} : Analyse critique des résultats, discussion des limitations, et présentation des perspectives d'évolution et des travaux futurs.

\textbf{Chapitre 8 - Conclusion Générale} : Synthèse des réalisations, impact du projet, et réflexions personnelles sur cette expérience formatrice.

Les annexes complètent ce rapport avec des détails techniques supplémentaires, le code source des algorithmes clés, et la documentation utilisateur complète.

% ===== CHAPITRE 2 : ÉTUDE DU BESOIN ET MÉTIER =====
\chapter{Étude du Besoin et Compréhension du Métier}

\section{Introduction}

La compréhension approfondie du domaine de l'acoustique sous-marine et des besoins spécifiques du secteur de la pêche constitue le fondement de ce projet. Ce chapitre présente une analyse détaillée du contexte métier, des technologies utilisées et des défis rencontrés par les professionnels du secteur.

\section{Principes de l'Acoustique Sous-Marine}

\subsection{Fondements Physiques}

L'acoustique sous-marine repose sur la propagation des ondes sonores dans l'eau, un milieu où le son se propage environ quatre fois plus rapidement que dans l'air ($\approx$1500 m/s). Cette propriété physique fondamentale permet l'utilisation du son comme principal moyen d'exploration des océans, où la lumière ne pénètre que sur quelques dizaines de mètres.

\subsubsection{Propagation des Ondes Acoustiques}

La vitesse du son dans l'eau de mer est donnée par l'équation empirique de Mackenzie (1981) :

\begin{equation}
c = 1448.96 + 4.591T - 5.304 \times 10^{-2}T^2 + 2.374 \times 10^{-4}T^3 + 1.340(S-35) + 1.630 \times 10^{-2}D + 1.675 \times 10^{-7}D^2
\end{equation}

où :
\begin{itemize}
\item $c$ : vitesse du son (m/s)
\item $T$ : température (°C)
\item $S$ : salinité (ppt)
\item $D$ : profondeur (m)
\end{itemize}

Cette équation montre l'influence complexe des paramètres environnementaux sur la propagation acoustique, nécessitant une calibration précise des instruments.

\subsubsection{Atténuation et Absorption}

L'atténuation du signal acoustique dans l'eau suit la loi :

\begin{equation}
TL = 20\log_{10}(r) + \alpha r
\end{equation}

où $TL$ est la perte de transmission, $r$ la distance et $\alpha$ le coefficient d'absorption qui dépend de la fréquence :

\begin{equation}
\alpha = \frac{0.11f^2}{1+f^2} + \frac{44f^2}{4100+f^2} + 2.75 \times 10^{-4}f^2 + 0.003
\end{equation}

\subsection{Caractéristiques des Cibles Biologiques}

Les poissons présentent des signatures acoustiques distinctives principalement dues à leur vessie natatoire, un organe rempli de gaz qui crée un fort contraste d'impédance acoustique avec l'eau environnante.

\subsubsection{Force de Cible (Target Strength)}

La force de cible (TS) d'un poisson peut être modélisée par :

\begin{equation}
TS = 20 \log_{10}(L) - b_{20}
\end{equation}

où $L$ est la longueur du poisson en centimètres et $b_{20}$ est une constante spécifique à l'espèce :

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|}
\hline
\textbf{Espèce} & \textbf{$b_{20}$ (dB)} & \textbf{Plage de validité (cm)} \\
\hline
Anchois & 72.6 & 5-20 \\
Sardine & 71.2 & 10-30 \\
Maquereau & 68.7 & 15-40 \\
Hareng & 71.9 & 10-35 \\
Thon & 65.4 & 50-200 \\
\hline
\end{tabular}
\caption{Constantes de force de cible pour différentes espèces}
\label{tab:target_strength}
\end{table}

\subsubsection{Comportement Acoustique des Bancs}

Les bancs de poissons présentent des caractéristiques acoustiques complexes :

\begin{itemize}
\item \textbf{Effet d'ombrage} : Les poissons en surface du banc masquent ceux en profondeur
\item \textbf{Résonance collective} : Amplification du signal à certaines fréquences
\item \textbf{Structure interne} : Organisation spatiale affectant la rétrodiffusion
\end{itemize}

\section{Technologies d'Échosondage Moderne}

\subsection{Évolution des Systèmes Simrad}

Kongsberg Maritime, à travers sa gamme Simrad, domine le marché des échosondeurs scientifiques avec une évolution technologique remarquable :

\begin{figure}[h]
\centering
\begin{tikzpicture}[scale=0.8]
% Timeline
\draw[thick,->] (0,0) -- (12,0) node[right] {Année};
\foreach \x/\year in {0/1990,3/2000,6/2010,9/2020,12/2030}
    \draw (\x,0.1) -- (\x,-0.1) node[below] {\year};

% EK60
\draw[fill=blue!30] (1.5,0.5) rectangle (5.5,1.5);
\node at (3.5,1) {EK60};

% EK70
\draw[fill=green!30] (4.5,1.5) rectangle (8.5,2.5);
\node at (6.5,2) {EK70};

% EK80
\draw[fill=red!30] (7.5,2.5) rectangle (11.5,3.5);
\node at (9.5,3) {EK80};

% Features
\node[align=left] at (3.5,0.3) {\small CW only};
\node[align=left] at (6.5,1.2) {\small CW+LFM};
\node[align=left] at (9.5,2.2) {\small CW+LFM+BB};
\end{tikzpicture}
\caption{Évolution temporelle des systèmes Simrad}
\label{fig:simrad_timeline}
\end{figure}

\subsubsection{Caractéristiques Techniques Détaillées}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Caractéristique} & \textbf{EK60} & \textbf{EK70} & \textbf{EK80} \\
\hline
Fréquences (kHz) & 18-710 & 18-710 & 10-1000 \\
Puissance max (W) & 2000 & 2000 & 4000 \\
Résolution (bits) & 16 & 16 & 24 \\
Taux d'échantillonnage & 48 kHz & 96 kHz & 1.5 MHz \\
Bande passante & Fixe & Variable & Adaptative \\
Modes de transmission & CW & CW, LFM & CW, LFM, BB \\
Portée max (m) & 1500 & 2000 & 3000 \\
\hline
\end{tabular}
\caption{Spécifications techniques des échosondeurs Simrad}
\label{tab:simrad_specs}
\end{table}

\subsection{Architecture des Fichiers .raw}

Les fichiers \file{.raw} suivent une structure binaire complexe nécessitant une compréhension approfondie pour leur exploitation :

\subsubsection{Structure des Datagrammes}

\begin{lstlisting}[language=C, caption=Structure d'un datagramme EK80]
typedef struct {
    uint32_t length;        // Longueur totale du datagramme
    char type[4];          // Type (CON0, RAW3, etc.)
    uint64_t timestamp;    // Timestamp UNIX en microsecondes
    uint16_t checksum;     // CRC16 pour validation
} DatagramHeader;

typedef struct {
    DatagramHeader header;
    uint32_t channel_id;   // Identifiant du canal
    uint32_t sample_count; // Nombre d'échantillons
    float frequency;       // Fréquence centrale
    float bandwidth;       // Bande passante
    float sample_rate;     // Taux d'échantillonnage
    float pulse_length;    // Durée de l'impulsion
    float power;          // Puissance transmise
    // Données complexes suivent...
} RAW3Datagram;
\end{lstlisting}

\subsubsection{Algorithme de Parsing Optimisé}

\begin{algorithm}
\caption{Parsing optimisé des fichiers .raw}
\label{alg:raw_parsing}
\begin{algorithmic}[1]
\STATE \textbf{Input:} Fichier .raw, Buffer size $B$
\STATE \textbf{Output:} Liste de datagrammes parsés
\STATE $file \gets$ MemoryMap(filename)
\STATE $datagrams \gets$ []
\STATE $offset \gets 0$
\WHILE{$offset < file.size$}
    \STATE $header \gets$ ReadHeader($file[offset:offset+16]$)
    \IF{ValidateChecksum($header$)}
        \STATE $datagram \gets$ ParseDatagram($file[offset:offset+header.length]$)
        \STATE $datagrams$.append($datagram$)
    \ELSE
        \STATE RecoverFromCorruption($file$, $offset$)
    \ENDIF
    \STATE $offset \gets offset + header.length$
\ENDWHILE
\RETURN $datagrams$
\end{algorithmic}
\end{algorithm}

\section{Analyse des Besoins du Secteur}

\subsection{Cartographie des Acteurs}

Notre étude approfondie du marché a permis d'identifier un écosystème complexe d'acteurs aux besoins variés :

\begin{figure}[h]
\centering
\begin{tikzpicture}[scale=0.9]
% Acteurs principaux
\node[draw, circle, fill=blue!20] (research) at (0,3) {Recherche};
\node[draw, circle, fill=green!20] (fisheries) at (3,3) {Pêcheries};
\node[draw, circle, fill=red!20] (commercial) at (6,3) {Commercial};
\node[draw, circle, fill=yellow!20] (env) at (9,3) {Environnement};

% Besoins
\node[draw, rectangle] (data) at (1.5,1) {Données};
\node[draw, rectangle] (analysis) at (4.5,1) {Analyse};
\node[draw, rectangle] (realtime) at (7.5,1) {Temps réel};

% Connexions
\draw[->] (research) -- (data);
\draw[->] (research) -- (analysis);
\draw[->] (fisheries) -- (analysis);
\draw[->] (fisheries) -- (realtime);
\draw[->] (commercial) -- (realtime);
\draw[->] (env) -- (data);
\draw[->] (env) -- (analysis);
\end{tikzpicture}
\caption{Écosystème des acteurs et leurs besoins principaux}
\label{fig:actors_ecosystem}
\end{figure}

\subsection{Analyse Quantitative des Besoins}

\subsubsection{Volume de Données par Secteur}

\begin{table}[h]
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Secteur} & \textbf{Données/jour} & \textbf{Stockage/an} & \textbf{Temps analyse} & \textbf{Budget IT} \\
\hline
Recherche & 100-500 GB & 50-100 TB & Semaines & 50-200k€ \\
Pêche commerciale & 10-50 GB & 5-20 TB & Heures & 10-50k€ \\
Gestion pêcheries & 50-200 GB & 20-50 TB & Jours & 20-100k€ \\
ONG environnement & 20-100 GB & 10-30 TB & Jours & 5-20k€ \\
\hline
\end{tabular}
\caption{Caractérisation quantitative des besoins par secteur}
\label{tab:sector_needs}
\end{table}

\subsection{Processus Métier Détaillés}

\subsubsection{Workflow Actuel d'Analyse}

\begin{figure}[h]
\centering
\begin{tikzpicture}[node distance=2cm, scale=0.8]
% Nodes
\node (start) [draw, ellipse] {Début};
\node (collect) [draw, rectangle, below of=start] {Collecte données};
\node (transfer) [draw, rectangle, below of=collect] {Transfert USB};
\node (convert) [draw, rectangle, below of=transfer] {Conversion format};
\node (process) [draw, rectangle, below of=convert] {Traitement};
\node (manual) [draw, rectangle, below of=process] {Analyse manuelle};
\node (report) [draw, rectangle, below of=manual] {Rapport};
\node (end) [draw, ellipse, below of=report] {Fin};

% Flows
\draw[->] (start) -- (collect) node[midway, right] {En mer};
\draw[->] (collect) -- (transfer) node[midway, right] {1-2 jours};
\draw[->] (transfer) -- (convert) node[midway, right] {2-3 jours};
\draw[->] (convert) -- (process) node[midway, right] {3-5 jours};
\draw[->] (process) -- (manual) node[midway, right] {1-2 semaines};
\draw[->] (manual) -- (report) node[midway, right] {2-3 jours};
\draw[->] (report) -- (end);

% Problèmes
\node[draw, dashed, red] at (8,0) {Goulot d'étranglement};
\draw[dashed, red, ->] (8,-0.5) -- (manual);
\end{tikzpicture}
\caption{Workflow actuel avec identification des goulots d'étranglement}
\label{fig:current_workflow}
\end{figure}

\section{Apprentissage Autonome et Montée en Compétences}

\subsection{Stratégie d'Apprentissage}

Face à la complexité du domaine, nous avons développé une approche structurée d'apprentissage :

\subsubsection{Phase 1 : Fondamentaux Théoriques}

\begin{table}[h]
\centering
\begin{tabular}{|l|l|c|}
\hline
\textbf{Ressource} & \textbf{Type} & \textbf{Temps investi} \\
\hline
"Fisheries Acoustics" (Simmonds \& MacLennan) & Livre & 40h \\
Articles ICES Journal of Marine Science & Publications & 30h \\
Cours en ligne "Underwater Acoustics" (MIT) & MOOC & 20h \\
Documentation Simrad EK80 & Manuel technique & 15h \\
Webinaires WGFAST & Conférences & 10h \\
\hline
\textbf{Total} & & \textbf{115h} \\
\hline
\end{tabular}
\caption{Ressources d'apprentissage et temps investi}
\label{tab:learning_resources}
\end{table}

\subsubsection{Phase 2 : Expérimentation Pratique}

\begin{lstlisting}[language=Python, caption=Premier script d'exploration des données .raw]
import struct
import numpy as np
import matplotlib.pyplot as plt

def explore_raw_file(filename):
    """Premier script pour comprendre la structure .raw"""
    with open(filename, 'rb') as f:
        # Lecture de l'en-tête
        header = f.read(32)
        
        # Décodage basique
        magic = header[:4].decode('ascii')
        version = struct.unpack('<I', header[4:8])[0]
        
        print(f"Magic: {magic}")
        print(f"Version: {version}")
        
        # Exploration des datagrammes
        datagram_count = 0
        datagram_types = {}
        
        while True:
            # Lire la longueur
            length_bytes = f.read(4)
            if not length_bytes:
                break
                
            length = struct.unpack('<I', length_bytes)[0]
            
            # Lire le type
            type_bytes = f.read(4)
            datagram_type = type_bytes.decode('ascii')
            
            # Compter les types
            datagram_types[datagram_type] = datagram_types.get(datagram_type, 0) + 1
            
            # Sauter le reste du datagramme
            f.seek(length - 8, 1)
            datagram_count += 1
            
        print(f"\nTotal datagrams: {datagram_count}")
        print("Datagram types:")
        for dtype, count in sorted(datagram_types.items()):
            print(f"  {dtype}: {count}")

# Test sur un fichier exemple
explore_raw_file('sample_data/D20210715-T164532.raw')
\end{lstlisting}

\subsection{Validation avec les Experts}

Les sessions de travail avec Stéphane Conti ont permis de valider et approfondir notre compréhension :

\begin{table}[h]
\centering
\begin{tabular}{|l|p{8cm}|}
\hline
\textbf{Session} & \textbf{Sujets abordés} \\
\hline
Session 1 (2h) & Principes physiques de l'écholocation, types de transducteurs \\
\hline
Session 2 (3h) & Structure des fichiers .raw, calibration des instruments \\
\hline
Session 3 (2h) & Identification visuelle des espèces sur échogrammes \\
\hline
Session 4 (3h) & Défis opérationnels en mer, cas d'usage réels \\
\hline
\end{tabular}
\caption{Sessions de formation avec l'expert acousticien}
\label{tab:expert_sessions}
\end{table}

\section{Définition des Exigences Détaillées}

\subsection{Exigences Fonctionnelles Prioritaires}

\subsubsection{EF1 : Plateforme d'Accès aux Données}

\begin{mdframed}[backgroundcolor=blue!10]
\textbf{ID :} EF1 \\
\textbf{Titre :} Plateforme web d'accès au dataset NOAA \\
\textbf{Description :} Interface permettant l'exploration, la recherche et le téléchargement efficace des fichiers acoustiques \\
\textbf{Critères d'acceptation :}
\begin{itemize}
\item Recherche par date avec granularité à la minute
\item Filtrage par navire, instrument, zone géographique
\item Téléchargement par lots (jusqu'à 100 fichiers)
\item Temps de réponse <200ms pour 10,000 résultats
\item Interface responsive mobile/desktop
\end{itemize}
\textbf{Priorité :} Haute
\end{mdframed}

\subsubsection{EF2 : Pipeline de Traitement Acoustique}

\begin{mdframed}[backgroundcolor=green!10]
\textbf{ID :} EF2 \\
\textbf{Titre :} Pipeline automatisé de traitement des fichiers .raw \\
\textbf{Description :} Système capable de lire, traiter et analyser les données acoustiques de manière optimisée \\
\textbf{Critères d'acceptation :}
\begin{itemize}
\item Support complet EK60/70/80
\item Calcul Sv avec calibration automatique
\item Génération échogrammes <5s par fichier
\item Détection automatique du fond
\item Export formats standards (NetCDF, CSV)
\end{itemize}
\textbf{Priorité :} Haute
\end{mdframed}

\subsubsection{EF3 : Classification par Intelligence Artificielle}

\begin{mdframed}[backgroundcolor=red!10]
\textbf{ID :} EF3 \\
\textbf{Titre :} Modèles de deep learning pour classification d'espèces \\
\textbf{Description :} Système d'IA capable d'identifier automatiquement les espèces marines à partir des échogrammes \\
\textbf{Critères d'acceptation :}
\begin{itemize}
\item Précision >90\% sur 3 espèces principales
\item Temps d'inférence <1s par échogramme
\item Confidence scores et explications
\item API REST pour intégration
\item Support du réentraînement incrémental
\end{itemize}
\textbf{Priorité :} Haute
\end{mdframed}

\subsection{Exigences Non-Fonctionnelles}

\subsubsection{Performance et Scalabilité}

\begin{table}[h]
\centering
\begin{tabular}{|l|l|l|}
\hline
\textbf{Métrique} & \textbf{Objectif} & \textbf{Mesure} \\
\hline
Latence API (p95) & <200ms & Prometheus \\
Throughput & 100 req/s & Load testing \\
Temps traitement fichier & <5s & Benchmarks \\
Utilisation CPU & <80\% & Monitoring \\
Utilisation mémoire & <4GB/instance & Metrics \\
Disponibilité & 99.9\% & Uptime monitoring \\
\hline
\end{tabular}
\caption{Objectifs de performance et méthodes de mesure}
\label{tab:performance_requirements}
\end{table}

\subsubsection{Sécurité et Conformité}

\begin{itemize}
\item \textbf{Authentification} : JWT avec refresh tokens
\item \textbf{Autorisation} : RBAC (Role-Based Access Control)
\item \textbf{Chiffrement} : TLS 1.3 pour les communications
\item \textbf{Audit} : Logs complets de toutes les actions
\item \textbf{RGPD} : Conformité pour les données utilisateurs
\item \textbf{Backup} : Sauvegarde quotidienne avec rétention 30 jours
\end{itemize}

\section{Modélisation du Domaine}

\subsection{Diagramme de Classes Métier}

\begin{figure}[h]
\centering
\begin{tikzpicture}[scale=0.7]
% Classes
\node[draw, rectangle] (vessel) at (0,4) {
\begin{tabular}{c}
\textbf{Vessel} \\
\hline
name: String \\
imo: String \\
callsign: String \\
\hline
getCruises() \\
\end{tabular}
};

\node[draw, rectangle] (cruise) at (5,4) {
\begin{tabular}{c}
\textbf{Cruise} \\
\hline
id: String \\
startDate: Date \\
endDate: Date \\
area: Polygon \\
\hline
getFiles() \\
\end{tabular}
};

\node[draw, rectangle] (file) at (10,4) {
\begin{tabular}{c}
\textbf{AcousticFile} \\
\hline
path: String \\
size: Long \\
timestamp: DateTime \\
format: FileFormat \\
\hline
parse() \\
getEchogram() \\
\end{tabular}
};

\node[draw, rectangle] (echogram) at (5,0) {
\begin{tabular}{c}
\textbf{Echogram} \\
\hline
data: Array2D \\
frequency: Float \\
range: Array \\
time: Array \\
\hline
calculateSv() \\
detectBottom() \\
\end{tabular}
};

\node[draw, rectangle] (species) at (0,0) {
\begin{tabular}{c}
\textbf{Species} \\
\hline
name: String \\
scientificName: String \\
targetStrength: Float \\
\hline
getAcousticSignature() \\
\end{tabular}
};

\node[draw, rectangle] (school) at (10,0) {
\begin{tabular}{c}
\textbf{FishSchool} \\
\hline
bounds: Rectangle \\
meanSv: Float \\
area: Float \\
depth: Float \\
\hline
classify() \\
\end{tabular}
};

% Relations
\draw[->] (vessel) -- node[above] {1..*} (cruise);
\draw[->] (cruise) -- node[above] {1..*} (file);
\draw[->] (file) -- node[right] {1} (echogram);
\draw[->] (echogram) -- node[above] {0..*} (school);
\draw[->] (school) -- node[above] {1} (species);
\end{tikzpicture}
\caption{Modèle de domaine simplifié}
\label{fig:domain_model}
\end{figure}

\section{Analyse d'Impact}

\subsection{Impact Économique}

\subsubsection{Analyse Coût-Bénéfice}

\begin{table}[h]
\centering
\begin{tabular}{|l|r|r|r|}
\hline
\textbf{Poste} & \textbf{Coût actuel/an} & \textbf{Avec solution} & \textbf{Économie} \\
\hline
Licences logicielles & 50,000€ & 0€ & 50,000€ \\
Expertise externe & 100,000€ & 20,000€ & 80,000€ \\
Temps d'analyse & 150,000€ & 30,000€ & 120,000€ \\
Infrastructure IT & 30,000€ & 10,000€ & 20,000€ \\
\hline
\textbf{Total} & \textbf{330,000€} & \textbf{60,000€} & \textbf{270,000€} \\
\hline
\end{tabular}
\caption{Analyse économique pour une organisation type}
\label{tab:cost_benefit}
\end{table}

\subsection{Impact Environnemental}

\subsubsection{Réduction des Prises Accessoires}

Notre solution peut contribuer significativement à la réduction des prises accessoires :

\begin{itemize}
\item \textbf{Identification précise} : Réduction de 30\% des erreurs d'identification
\item \textbf{Temps réel} : Décisions de pêche plus éclairées
\item \textbf{Zones protégées} : Meilleur respect des sanctuaires marins
\item \textbf{Espèces menacées} : Détection et évitement améliorés
\end{itemize}

\section{Conclusion du Chapitre}

Cette étude approfondie du métier et des besoins a révélé un marché en attente d'innovation, avec des utilisateurs confrontés à des outils inadaptés et des processus inefficaces. L'absence de solution intégrée représente une opportunité unique de créer un impact significatif dans le domaine de la gestion durable des ressources marines.

Notre approche d'apprentissage autonome, combinée à l'expertise de Proteyus, nous a permis d'acquérir rapidement les compétences nécessaires et de définir des spécifications techniques alignées avec les besoins réels du terrain. Cette base solide constitue le fondement pour le développement de notre solution innovante.

Les exigences définies couvrent l'ensemble des besoins identifiés, depuis l'accès facilité aux données jusqu'à la classification automatique par IA, en passant par l'optimisation des performances et la garantie de sécurité. La modélisation du domaine et l'analyse d'impact confirment la pertinence et le potentiel de notre approche.