# Backend de l'Application d'Analyse Acoustique Marine

Ce répertoire contient le code source du backend de l'application d'analyse acoustique marine, développé avec FastAPI.

## Structure du Projet

```
backend/
├── app/
│   ├── api/
│   │   ├── __init__.py
│   │   ├── data_management.py
│   │   ├── visualization.py
│   │   ├── feature_extraction.py
│   │   ├── annotation.py
│   │   ├── dataset.py
│   │   ├── ml_models.py
│   │   └── analysis.py
│   ├── core/
│   │   ├── __init__.py
│   │   └── config.py
│   ├── models/
│   │   ├── __init__.py
│   │   └── schemas.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── raw_data_service.py
│   │   ├── visualization_service.py
│   │   ├── feature_extraction_service.py
│   │   ├── annotation_service.py
│   │   ├── dataset_service.py
│   │   ├── ml_model_service.py
│   │   └── analysis_service.py
│   └── main.py
└── requirements.txt
└── README.md
```

## Configuration et Exécution Locale

Suivez les étapes ci-dessous pour configurer et exécuter le backend sur votre machine locale.

### 1. Prérequis

Assurez-vous d'avoir Python 3.8+ installé sur votre système.

### 2. Cloner le Dépôt (si applicable)

Si ce code vous a été fourni via un dépôt Git, clonez-le :

```bash
git clone <URL_DU_DEPOT>
cd <NOM_DU_REPERTOIRE_CLONE>/backend
```

Si vous avez reçu les fichiers directement, naviguez simplement vers le répertoire `backend`.

### 3. Créer un Environnement Virtuel (Recommandé)

Il est fortement recommandé de créer un environnement virtuel pour isoler les dépendances du projet :

```bash
python3 -m venv venv
source venv/bin/activate  # Sur Linux/macOS
# ou
.\venv\Scripts\activate   # Sur Windows
```

### 4. Installer les Dépendances

Installez toutes les bibliothèques Python nécessaires à partir du fichier `requirements.txt` :

```bash
pip install -r requirements.txt
```

### 5. Exécuter l'Application FastAPI

Une fois les dépendances installées, vous pouvez lancer le serveur FastAPI en utilisant Uvicorn :

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

- `--host 0.0.0.0`: Rend l'application accessible depuis d'autres appareils sur votre réseau local (utile pour les tests depuis un frontend sur une autre machine).
- `--port 8000`: Spécifie le port sur lequel l'application s'exécutera. Vous pouvez le changer si le port 8000 est déjà utilisé.
- `--reload`: Active le rechargement automatique du serveur lorsque vous modifiez le code source (utile pendant le développement).

### 6. Accéder à l'API

Une fois le serveur lancé, l'API sera accessible à l'adresse suivante :

- **Documentation interactive (Swagger UI)**: `http://localhost:8000/docs`
- **Documentation alternative (ReDoc)**: `http://localhost:8000/redoc`

Vous pouvez utiliser ces interfaces pour explorer les endpoints disponibles et tester l'API.

### 7. Nettoyage (Optionnel)

Pour désactiver l'environnement virtuel :

```bash
deactivate
```

Pour supprimer l'environnement virtuel et les dépendances installées :

```bash
deactivate
rm -rf venv  # Sur Linux/macOS
# ou
rmdir /s /q venv # Sur Windows
```

---

N'hésitez pas à me contacter si vous rencontrez des problèmes ou si vous avez des questions. Bon développement !

